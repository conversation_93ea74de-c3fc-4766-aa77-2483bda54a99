<!--pages/test-runner/test-runner.wxml-->
<view class="container">
  <!-- 测试控制面板 -->
  <view class="control-panel">
    <view class="panel-title">测试控制面板</view>
    
    <!-- 测试类型选择 -->
    <view class="test-types">
      <view wx:for="{{testTypes}}" wx:key="id" class="test-type-item">
        <switch checked="{{item.enabled}}" 
                data-index="{{index}}" 
                bindchange="onToggleTestType" />
        <text class="test-type-name">{{item.name}}</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn primary" 
              bindtap="onRunTests" 
              disabled="{{isRunning}}"
              loading="{{isRunning}}">
        {{isRunning ? '运行中...' : '运行测试'}}
      </button>
      
      <button class="action-btn" bindtap="onRunPerformanceTest" disabled="{{isRunning}}">
        性能测试
      </button>
      
      <button class="action-btn" bindtap="onRunCompatibilityTest" disabled="{{isRunning}}">
        兼容性测试
      </button>
    </view>
  </view>

  <!-- 测试结果 -->
  <view wx:if="{{testResults}}" class="test-results">
    <view class="results-title">测试结果</view>
    
    <!-- 统计信息 -->
    <view class="results-summary">
      <view class="summary-item">
        <text class="summary-label">总计</text>
        <text class="summary-value">{{testResults.summary.total}}</text>
      </view>
      <view class="summary-item success">
        <text class="summary-label">通过</text>
        <text class="summary-value">{{testResults.summary.passed}}</text>
      </view>
      <view class="summary-item error">
        <text class="summary-label">失败</text>
        <text class="summary-value">{{testResults.summary.failed}}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">耗时</text>
        <text class="summary-value">{{testResults.summary.duration}}ms</text>
      </view>
    </view>
    
    <!-- 详细结果 -->
    <view class="results-details">
      <view wx:for="{{testResults.details}}" wx:key="name" 
            class="result-item {{item.status}}">
        <view class="result-header">
          <text class="result-name">{{item.name}}</text>
          <text class="result-status">{{item.status === 'passed' ? '✅' : '❌'}}</text>
        </view>
        <view class="result-meta">
          <text class="result-type">{{item.type}}</text>
          <text class="result-duration">{{item.duration}}ms</text>
        </view>
        <view wx:if="{{item.error}}" class="result-error">
          <text>{{item.error}}</text>
        </view>
      </view>
    </view>
    
    <!-- 导出按钮 -->
    <view class="export-section">
      <button class="export-btn" bindtap="onExportReport">
        导出测试报告
      </button>
    </view>
  </view>

  <!-- 测试日志 -->
  <view class="test-logs">
    <view class="logs-header">
      <text class="logs-title">测试日志</text>
      <button class="clear-btn" bindtap="onClearLogs">清空</button>
    </view>
    
    <scroll-view class="logs-content" scroll-y="true" scroll-top="{{logsScrollTop}}">
      <view wx:for="{{logs}}" wx:key="index" class="log-item">
        <text class="log-time">{{item.time}}</text>
        <text class="log-message">{{item.message}}</text>
      </view>
      
      <view wx:if="{{logs.length === 0}}" class="empty-logs">
        <text>暂无日志</text>
      </view>
    </scroll-view>
  </view>

  <!-- 测试数据展示（用于性能测试） -->
  <view wx:if="{{testData && testData.length > 0}}" class="test-data">
    <view class="data-title">测试数据 ({{testData.length}}项)</view>
    <scroll-view class="data-list" scroll-y="true">
      <view wx:for="{{testData}}" wx:key="id" class="data-item">
        <text class="data-title">{{item.title}}</text>
        <text class="data-desc">{{item.description}}</text>
      </view>
    </scroll-view>
  </view>
</view>
