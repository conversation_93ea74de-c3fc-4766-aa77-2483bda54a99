// app.js
const { SyncManager } = require('./utils/offline.js')
const { DeviceManager } = require('./utils/device.js')
const { getConfig } = require('./config/env.js')

App({
  onLaunch() {
    // 获取环境配置
    const envConfig = getConfig()
    console.log('当前环境配置:', envConfig)

    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        // 使用环境配置中的云环境ID
        env: envConfig.cloudEnvId,
        traceUser: true,
      })
      console.log('云开发初始化完成，环境ID:', envConfig.cloudEnvId)
    }

    // 初始化设备信息
    this.initDevice()

    // 初始化性能监控
    this.initPerformance()

    // 检查用户登录状态
    this.checkLoginStatus()

    // 自动同步数据
    this.autoSync()
  },

  onShow() {
    // 小程序显示时的处理
    console.log('小程序显示')

    // 自动同步数据
    this.autoSync()
  },

  onHide() {
    // 小程序隐藏时的处理
    console.log('小程序隐藏')
  },

  // 初始化设备信息
  initDevice() {
    try {
      // 创建设备管理器实例
      const deviceManager = new DeviceManager()
      this.globalData.deviceInfo = deviceManager.getSystemInfo()
      console.log('设备信息初始化完成:', this.globalData.deviceInfo)
    } catch (error) {
      console.error('设备信息初始化失败:', error)
    }
  },

  // 初始化性能监控
  initPerformance() {
    try {
      // 启用内存监控
      if (wx.onMemoryWarning) {
        wx.onMemoryWarning(() => {
          console.warn('内存不足警告')
          // 清理缓存
          try {
            wx.clearStorage()
            console.log('缓存清理完成')
          } catch (error) {
            console.error('清理缓存失败:', error)
          }
        })
      }
      console.log('性能监控初始化完成')
    } catch (error) {
      console.error('性能监控初始化失败:', error)
    }
  },

  // 自动同步数据
  async autoSync() {
    try {
      const result = await SyncManager.autoSync()
      if (result.success && result.offlineActions) {
        console.log('数据同步完成:', result.offlineActions)
      }
    } catch (error) {
      console.error('数据同步失败:', error)
    }
  },

  // 检查用户登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo && userInfo.openid) {
      this.globalData.userInfo = userInfo
      this.globalData.isLogin = true
    } else {
      this.globalData.isLogin = false
    }
  },

  // 用户登录
  async login() {
    try {
      // 调用云函数进行登录
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {}
      })
      
      if (result.result.success) {
        const userInfo = result.result.data
        this.globalData.userInfo = userInfo
        this.globalData.isLogin = true
        
        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', userInfo)
        
        return userInfo
      } else {
        throw new Error(result.result.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      })
      throw error
    }
  },

  // 获取用户信息
  getUserInfo() {
    return this.globalData.userInfo
  },

  // 更新用户积分
  updateUserPoints(points) {
    if (this.globalData.userInfo) {
      this.globalData.userInfo.points = points
      wx.setStorageSync('userInfo', this.globalData.userInfo)
    }
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLogin: false,
    deviceInfo: null,
    systemInfo: null
  }
})
