// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { shareCode } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    if (!shareCode) {
      return {
        success: false,
        message: '分享码不能为空'
      }
    }

    if (!openid) {
      return {
        success: false,
        message: '用户未登录'
      }
    }

    // 获取系统配置
    const config = await getConfig()
    if (!config.enable_share_reward) {
      return {
        success: false,
        message: '分享奖励功能已关闭'
      }
    }

    // 查询分享记录
    const shareResult = await db.collection('share_records')
      .where({
        share_code: shareCode,
        is_active: true
      })
      .get()

    if (shareResult.data.length === 0) {
      return {
        success: false,
        message: '无效的分享码'
      }
    }

    const shareRecord = shareResult.data[0]

    // 检查是否是自己的分享码
    if (shareRecord.sharer_openid === openid) {
      return {
        success: false,
        message: '不能使用自己的分享码'
      }
    }

    // 检查是否已经使用过分享码
    const existingResult = await db.collection('share_records')
      .where({
        sharer_openid: shareRecord.sharer_openid,
        invitee_openid: openid,
        reward_given: true
      })
      .get()

    if (existingResult.data.length > 0) {
      return {
        success: false,
        message: '您已经使用过该用户的分享码'
      }
    }

    // 检查当前用户是否是新用户（注册时间在24小时内）
    const userResult = await db.collection('users')
      .where({
        _openid: openid
      })
      .get()

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    const user = userResult.data[0]
    const now = new Date()
    const userCreateTime = new Date(user.createTime)
    const timeDiff = now.getTime() - userCreateTime.getTime()
    const hoursDiff = timeDiff / (1000 * 60 * 60)

    // 只有24小时内的新用户才能获得邀请奖励
    if (hoursDiff > 24) {
      return {
        success: false,
        message: '分享奖励仅限新用户24小时内使用'
      }
    }

    // 开始事务处理
    const transaction = await db.startTransaction()

    try {
      // 1. 给分享者发放奖励
      const sharerResult = await transaction.collection('users')
        .where({
          _openid: shareRecord.sharer_openid
        })
        .get()

      if (sharerResult.data.length > 0) {
        const sharer = sharerResult.data[0]
        const newSharerPoints = sharer.points + config.share_reward_points

        await transaction.collection('users')
          .doc(sharer._id)
          .update({
            data: {
              points: newSharerPoints
            }
          })

        // 记录分享者积分流水
        await transaction.collection('points_log')
          .add({
            data: {
              user_openid: shareRecord.sharer_openid,
              amount: config.share_reward_points,
              balance: newSharerPoints,
              type: 'share_reward',
              description: '邀请新用户奖励',
              related_user: openid,
              createTime: now
            }
          })
      }

      // 2. 给被邀请者发放奖励
      const newInviteePoints = user.points + config.invited_user_points

      await transaction.collection('users')
        .doc(user._id)
        .update({
          data: {
            points: newInviteePoints
          }
        })

      // 记录被邀请者积分流水
      await transaction.collection('points_log')
        .add({
          data: {
            user_openid: openid,
            amount: config.invited_user_points,
            balance: newInviteePoints,
            type: 'invite_reward',
            description: '新用户邀请奖励',
            related_user: shareRecord.sharer_openid,
            createTime: now
          }
        })

      // 3. 更新分享记录
      await transaction.collection('share_records')
        .doc(shareRecord._id)
        .update({
          data: {
            invitee_openid: openid,
            reward_given: true,
            reward_time: now,
            sharer_reward: config.share_reward_points,
            invitee_reward: config.invited_user_points
          }
        })

      // 提交事务
      await transaction.commit()

      return {
        success: true,
        message: '分享奖励发放成功',
        data: {
          sharerReward: config.share_reward_points,
          inviteeReward: config.invited_user_points,
          newPoints: newInviteePoints
        }
      }

    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback()
      throw transactionError
    }

  } catch (error) {
    console.error('处理分享邀请失败:', error)
    return {
      success: false,
      message: '处理分享邀请失败，请稍后重试'
    }
  }
}

// 获取系统配置
async function getConfig() {
  try {
    const result = await db.collection('config').doc('main').get()
    return result.data || {}
  } catch (error) {
    console.error('获取配置失败:', error)
    return {
      enable_share_reward: true,
      share_reward_points: 30,
      invited_user_points: 20
    }
  }
}
