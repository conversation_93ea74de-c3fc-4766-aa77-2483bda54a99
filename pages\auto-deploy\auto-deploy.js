// pages/auto-deploy/auto-deploy.js
const { getConfig } = require('../../config/env.js')

Page({
  data: {
    envConfig: null,
    deploySteps: [
      { 
        id: 'check-env', 
        name: '检查环境配置', 
        status: 'pending', 
        description: '验证云开发环境是否正确配置'
      },
      { 
        id: 'init-database', 
        name: '初始化数据库', 
        status: 'pending', 
        description: '创建数据库集合并导入初始数据'
      },
      { 
        id: 'check-functions', 
        name: '检查云函数', 
        status: 'pending', 
        description: '验证云函数是否已正确部署'
      },
      { 
        id: 'test-apis', 
        name: '测试API接口', 
        status: 'pending', 
        description: '测试核心功能是否正常工作'
      },
      { 
        id: 'final-check', 
        name: '最终验证', 
        status: 'pending', 
        description: '完整的功能验证测试'
      }
    ],
    currentStep: -1,
    deploying: false,
    deployComplete: false,
    deployResults: []
  },

  onLoad() {
    this.initEnvConfig()
  },

  // 初始化环境配置
  initEnvConfig() {
    const envConfig = getConfig()
    this.setData({
      envConfig
    })
  },

  // 开始自动部署
  async onStartDeploy() {
    this.setData({
      deploying: true,
      deployComplete: false,
      currentStep: 0,
      deployResults: []
    })

    try {
      // 执行部署步骤
      for (let i = 0; i < this.data.deploySteps.length; i++) {
        await this.executeStep(i)
      }

      this.setData({
        deployComplete: true,
        deploying: false
      })

      wx.showToast({
        title: '部署完成！',
        icon: 'success'
      })

    } catch (error) {
      console.error('部署失败:', error)
      this.setData({
        deploying: false
      })
      
      wx.showModal({
        title: '部署失败',
        content: error.message || '部署过程中出现错误',
        showCancel: false
      })
    }
  },

  // 执行单个步骤
  async executeStep(stepIndex) {
    const steps = this.data.deploySteps
    const step = steps[stepIndex]
    
    // 更新步骤状态为进行中
    step.status = 'running'
    this.setData({
      currentStep: stepIndex,
      deploySteps: steps
    })

    try {
      let result
      
      switch (step.id) {
        case 'check-env':
          result = await this.checkEnvironment()
          break
        case 'init-database':
          result = await this.initializeDatabase()
          break
        case 'check-functions':
          result = await this.checkCloudFunctions()
          break
        case 'test-apis':
          result = await this.testAPIs()
          break
        case 'final-check':
          result = await this.finalCheck()
          break
        default:
          throw new Error('未知的部署步骤')
      }

      // 更新步骤状态为成功
      step.status = 'success'
      step.result = result
      
      // 记录结果
      const results = this.data.deployResults
      results.push({
        step: step.name,
        success: true,
        message: result.message,
        details: result.details
      })

      this.setData({
        deploySteps: steps,
        deployResults: results
      })

      // 添加延迟让用户看到进度
      await this.delay(1000)

    } catch (error) {
      // 更新步骤状态为失败
      step.status = 'failed'
      step.error = error.message
      
      // 记录错误
      const results = this.data.deployResults
      results.push({
        step: step.name,
        success: false,
        message: error.message
      })

      this.setData({
        deploySteps: steps,
        deployResults: results
      })

      throw error
    }
  },

  // 检查环境配置
  async checkEnvironment() {
    try {
      // 检查云开发是否已初始化
      if (!wx.cloud) {
        throw new Error('云开发未初始化')
      }

      // 检查数据库连接
      const db = wx.cloud.database()
      await db.collection('test').limit(1).get()

      return {
        message: '环境配置检查通过',
        details: {
          cloudEnv: this.data.envConfig.cloudEnvId,
          debug: this.data.envConfig.debug
        }
      }
    } catch (error) {
      if (error.errMsg && error.errMsg.includes('collection not exist')) {
        // 集合不存在是正常的，说明数据库连接正常
        return {
          message: '环境配置检查通过',
          details: {
            cloudEnv: this.data.envConfig.cloudEnvId,
            debug: this.data.envConfig.debug
          }
        }
      }
      throw new Error(`环境检查失败: ${error.message}`)
    }
  },

  // 初始化数据库
  async initializeDatabase() {
    try {
      // 调用数据库初始化云函数
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'init' }
      })

      if (!result.result.success) {
        throw new Error(result.result.message)
      }

      return {
        message: '数据库初始化成功',
        details: result.result.results
      }
    } catch (error) {
      throw new Error(`数据库初始化失败: ${error.message}`)
    }
  },

  // 检查云函数
  async checkCloudFunctions() {
    const requiredFunctions = [
      'getCategories',
      'getRecommendMaterials', 
      'login',
      'getUserInfo'
    ]

    const results = []
    
    for (const funcName of requiredFunctions) {
      try {
        await wx.cloud.callFunction({
          name: funcName,
          data: {}
        })
        results.push({ function: funcName, status: 'success' })
      } catch (error) {
        results.push({ function: funcName, status: 'failed', error: error.message })
      }
    }

    const failedFunctions = results.filter(r => r.status === 'failed')
    if (failedFunctions.length > 0) {
      throw new Error(`以下云函数未部署或有错误: ${failedFunctions.map(f => f.function).join(', ')}`)
    }

    return {
      message: '云函数检查通过',
      details: results
    }
  },

  // 测试API接口
  async testAPIs() {
    const tests = [
      {
        name: '获取分类数据',
        test: () => wx.cloud.callFunction({ name: 'getCategories', data: {} })
      },
      {
        name: '获取推荐资料',
        test: () => wx.cloud.callFunction({ 
          name: 'getRecommendMaterials', 
          data: { page: 1, limit: 5 } 
        })
      }
    ]

    const results = []
    
    for (const test of tests) {
      try {
        const result = await test.test()
        if (result.result && result.result.success !== false) {
          results.push({ test: test.name, status: 'success' })
        } else {
          results.push({ test: test.name, status: 'failed', error: 'API返回错误' })
        }
      } catch (error) {
        results.push({ test: test.name, status: 'failed', error: error.message })
      }
    }

    const failedTests = results.filter(r => r.status === 'failed')
    if (failedTests.length > 0) {
      throw new Error(`以下API测试失败: ${failedTests.map(t => t.test).join(', ')}`)
    }

    return {
      message: 'API接口测试通过',
      details: results
    }
  },

  // 最终检查
  async finalCheck() {
    try {
      // 检查数据库状态
      const dbResult = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'check' }
      })

      if (!dbResult.result.success) {
        throw new Error('数据库状态检查失败')
      }

      return {
        message: '最终验证通过，部署成功！',
        details: {
          database: dbResult.result.collections,
          timestamp: new Date().toLocaleString()
        }
      }
    } catch (error) {
      throw new Error(`最终检查失败: ${error.message}`)
    }
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // 查看详细结果
  onViewDetails(e) {
    const { index } = e.currentTarget.dataset
    const result = this.data.deployResults[index]
    
    wx.showModal({
      title: result.step,
      content: JSON.stringify(result.details || result.message, null, 2),
      showCancel: false
    })
  },

  // 重新部署
  onRedeploy() {
    wx.showModal({
      title: '确认重新部署',
      content: '这将重新执行所有部署步骤，确认继续？',
      success: (res) => {
        if (res.confirm) {
          this.onStartDeploy()
        }
      }
    })
  },

  // 获取状态图标
  getStatusIcon(status) {
    switch (status) {
      case 'success':
        return '✅'
      case 'failed':
        return '❌'
      case 'running':
        return '🔄'
      default:
        return '⏳'
    }
  }
})
