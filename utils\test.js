/**
 * 测试工具类
 * 提供单元测试、集成测试、性能测试等功能
 */

/**
 * 测试框架类
 */
class TestFramework {
  constructor() {
    this.tests = []
    this.results = []
    this.startTime = 0
    this.endTime = 0
  }

  /**
   * 添加测试用例
   * @param {string} name 测试名称
   * @param {Function} testFn 测试函数
   */
  test(name, testFn) {
    this.tests.push({
      name,
      testFn,
      type: 'unit'
    })
  }

  /**
   * 添加集成测试用例
   * @param {string} name 测试名称
   * @param {Function} testFn 测试函数
   */
  integration(name, testFn) {
    this.tests.push({
      name,
      testFn,
      type: 'integration'
    })
  }

  /**
   * 添加性能测试用例
   * @param {string} name 测试名称
   * @param {Function} testFn 测试函数
   */
  performance(name, testFn) {
    this.tests.push({
      name,
      testFn,
      type: 'performance'
    })
  }

  /**
   * 运行所有测试
   */
  async runAll() {
    console.log('🚀 开始运行测试...')
    this.startTime = Date.now()
    this.results = []

    for (const test of this.tests) {
      await this.runSingleTest(test)
    }

    this.endTime = Date.now()
    this.printResults()
  }

  /**
   * 运行单个测试
   * @param {Object} test 测试对象
   */
  async runSingleTest(test) {
    const startTime = Date.now()
    let result = {
      name: test.name,
      type: test.type,
      status: 'pending',
      duration: 0,
      error: null
    }

    try {
      console.log(`🧪 运行测试: ${test.name}`)
      await test.testFn()
      result.status = 'passed'
      console.log(`✅ 测试通过: ${test.name}`)
    } catch (error) {
      result.status = 'failed'
      result.error = error.message
      console.error(`❌ 测试失败: ${test.name}`, error)
    }

    result.duration = Date.now() - startTime
    this.results.push(result)
  }

  /**
   * 打印测试结果
   */
  printResults() {
    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.status === 'passed').length
    const failedTests = this.results.filter(r => r.status === 'failed').length
    const totalDuration = this.endTime - this.startTime

    console.log('\n📊 测试结果统计:')
    console.log(`总测试数: ${totalTests}`)
    console.log(`通过: ${passedTests}`)
    console.log(`失败: ${failedTests}`)
    console.log(`总耗时: ${totalDuration}ms`)

    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:')
      this.results
        .filter(r => r.status === 'failed')
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`)
        })
    }

    console.log('\n📈 性能测试结果:')
    this.results
      .filter(r => r.type === 'performance')
      .forEach(r => {
        console.log(`  - ${r.name}: ${r.duration}ms`)
      })
  }

  /**
   * 获取测试报告
   */
  getReport() {
    return {
      summary: {
        total: this.results.length,
        passed: this.results.filter(r => r.status === 'passed').length,
        failed: this.results.filter(r => r.status === 'failed').length,
        duration: this.endTime - this.startTime
      },
      details: this.results
    }
  }
}

/**
 * 断言工具类
 */
class Assert {
  /**
   * 断言相等
   */
  static equal(actual, expected, message = '') {
    if (actual !== expected) {
      throw new Error(`断言失败: ${message || `期望 ${expected}，实际 ${actual}`}`)
    }
  }

  /**
   * 断言不相等
   */
  static notEqual(actual, expected, message = '') {
    if (actual === expected) {
      throw new Error(`断言失败: ${message || `期望不等于 ${expected}，实际 ${actual}`}`)
    }
  }

  /**
   * 断言为真
   */
  static isTrue(value, message = '') {
    if (value !== true) {
      throw new Error(`断言失败: ${message || `期望为 true，实际 ${value}`}`)
    }
  }

  /**
   * 断言为假
   */
  static isFalse(value, message = '') {
    if (value !== false) {
      throw new Error(`断言失败: ${message || `期望为 false，实际 ${value}`}`)
    }
  }

  /**
   * 断言为null
   */
  static isNull(value, message = '') {
    if (value !== null) {
      throw new Error(`断言失败: ${message || `期望为 null，实际 ${value}`}`)
    }
  }

  /**
   * 断言不为null
   */
  static isNotNull(value, message = '') {
    if (value === null) {
      throw new Error(`断言失败: ${message || '期望不为 null'}`)
    }
  }

  /**
   * 断言为undefined
   */
  static isUndefined(value, message = '') {
    if (value !== undefined) {
      throw new Error(`断言失败: ${message || `期望为 undefined，实际 ${value}`}`)
    }
  }

  /**
   * 断言不为undefined
   */
  static isNotUndefined(value, message = '') {
    if (value === undefined) {
      throw new Error(`断言失败: ${message || '期望不为 undefined'}`)
    }
  }

  /**
   * 断言包含
   */
  static contains(container, item, message = '') {
    if (Array.isArray(container)) {
      if (!container.includes(item)) {
        throw new Error(`断言失败: ${message || `数组不包含 ${item}`}`)
      }
    } else if (typeof container === 'string') {
      if (container.indexOf(item) === -1) {
        throw new Error(`断言失败: ${message || `字符串不包含 ${item}`}`)
      }
    } else {
      throw new Error('断言失败: 容器必须是数组或字符串')
    }
  }

  /**
   * 断言抛出异常
   */
  static async throws(fn, message = '') {
    let threw = false
    try {
      await fn()
    } catch (error) {
      threw = true
    }
    
    if (!threw) {
      throw new Error(`断言失败: ${message || '期望抛出异常'}`)
    }
  }
}

/**
 * 性能测试工具类
 */
class PerformanceTest {
  /**
   * 测试函数执行时间
   * @param {Function} fn 要测试的函数
   * @param {number} iterations 执行次数
   */
  static async measureTime(fn, iterations = 1) {
    const startTime = Date.now()
    
    for (let i = 0; i < iterations; i++) {
      await fn()
    }
    
    const endTime = Date.now()
    const totalTime = endTime - startTime
    const averageTime = totalTime / iterations
    
    return {
      totalTime,
      averageTime,
      iterations
    }
  }

  /**
   * 测试内存使用
   */
  static measureMemory() {
    try {
      const memoryInfo = wx.getStorageInfoSync()
      return {
        currentSize: memoryInfo.currentSize,
        limitSize: memoryInfo.limitSize,
        keys: memoryInfo.keys
      }
    } catch (error) {
      console.error('获取内存信息失败:', error)
      return null
    }
  }

  /**
   * 压力测试
   * @param {Function} fn 要测试的函数
   * @param {number} concurrency 并发数
   * @param {number} duration 持续时间（毫秒）
   */
  static async stressTest(fn, concurrency = 10, duration = 5000) {
    const startTime = Date.now()
    const results = []
    let completed = 0
    let errors = 0

    const runTest = async () => {
      while (Date.now() - startTime < duration) {
        try {
          await fn()
          completed++
        } catch (error) {
          errors++
        }
      }
    }

    // 启动并发测试
    const promises = Array(concurrency).fill().map(() => runTest())
    await Promise.all(promises)

    const totalTime = Date.now() - startTime
    const throughput = completed / (totalTime / 1000) // 每秒完成数

    return {
      completed,
      errors,
      duration: totalTime,
      throughput: Math.round(throughput * 100) / 100,
      errorRate: errors / (completed + errors)
    }
  }
}

// 创建全局测试实例
const testFramework = new TestFramework()

module.exports = {
  TestFramework,
  Assert,
  PerformanceTest,
  testFramework
}
