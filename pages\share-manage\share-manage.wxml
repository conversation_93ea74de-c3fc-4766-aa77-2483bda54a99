<!--pages/share-manage/share-manage.wxml-->
<view class="container">
  <!-- 未登录状态 -->
  <view wx:if="{{!isLogin}}" class="login-tip">
    <image src="/images/empty-materials.svg" class="empty-icon" mode="aspectFit"></image>
    <text class="empty-text">请先登录后查看分享管理</text>
    <button class="login-btn" bindtap="onGoLogin">去登录</button>
  </view>

  <!-- 已登录状态 -->
  <view wx:else class="content">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <!-- 内容区域 -->
    <view wx:else>
      <!-- 分享码卡片 -->
      <view class="share-card">
        <view class="card-header">
          <text class="card-title">我的分享码</text>
          <button wx:if="{{generating}}" class="refresh-btn loading" disabled>生成中...</button>
          <button wx:else class="refresh-btn" bindtap="generateOrGetShareCode">刷新</button>
        </view>
        
        <view class="share-code-section">
          <view class="share-code">{{currentShareCode || '暂无分享码'}}</view>
          <view class="share-actions">
            <button class="action-btn" bindtap="onCopyShareCode" disabled="{{!currentShareCode}}">复制分享码</button>
            <button class="action-btn primary" bindtap="onCopyShareUrl" disabled="{{!currentShareCode}}">复制分享内容</button>
          </view>
        </view>

        <view class="share-tip">
          <text>邀请好友注册可获得积分奖励，好友也会获得新用户奖励</text>
        </view>
      </view>

      <!-- 统计卡片 -->
      <view wx:if="{{shareStats}}" class="stats-card">
        <view class="card-header">
          <text class="card-title">分享统计</text>
        </view>
        
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{shareStats.totalShares}}</text>
            <text class="stat-label">总分享次数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{shareStats.totalInvites}}</text>
            <text class="stat-label">成功邀请</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{shareStats.totalClicks}}</text>
            <text class="stat-label">总点击次数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{shareStats.totalRewardPoints}}</text>
            <text class="stat-label">获得积分</text>
          </view>
        </view>
      </view>

      <!-- 分享记录 -->
      <view wx:if="{{shareStats && shareStats.recentShares.length > 0}}" class="records-card">
        <view class="card-header">
          <text class="card-title">最近分享记录</text>
        </view>
        
        <view class="records-list">
          <view wx:for="{{shareStats.recentShares}}" wx:key="shareCode" 
                class="record-item" bindtap="onViewShareDetail" data-share="{{item}}">
            <view class="record-info">
              <view class="record-code">分享码：{{item.shareCode}}</view>
              <view class="record-time">{{item.createTime}}</view>
            </view>
            <view class="record-stats">
              <text class="record-stat">点击{{item.clickCount}}</text>
              <text class="record-stat">邀请{{item.successCount}}</text>
              <text class="record-status {{item.isActive ? 'active' : 'inactive'}}">
                {{item.isActive ? '有效' : '已过期'}}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:if="{{!shareStats || shareStats.totalShares === 0}}" class="empty-state">
        <image src="/images/empty-materials.svg" class="empty-icon" mode="aspectFit"></image>
        <text class="empty-text">还没有分享记录</text>
        <text class="empty-desc">分享给好友，一起获得积分奖励</text>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="share-btn" bindtap="onShareToFriend" disabled="{{!currentShareCode}}">
          <image src="/images/icon-share.svg" class="btn-icon"></image>
          <text>分享给好友</text>
        </button>
      </view>
    </view>
  </view>
</view>
