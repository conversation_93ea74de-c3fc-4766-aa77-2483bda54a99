<!--pages/deploy-tracker/deploy-tracker.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">📊 部署进度跟踪</text>
    <text class="page-desc">实时监控云函数部署状态</text>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stats-item">
        <text class="stats-number">{{deployStats.deployed}}</text>
        <text class="stats-label">已部署</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{deployStats.pending}}</text>
        <text class="stats-label">待部署</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{deployStats.failed}}</text>
        <text class="stats-label">失败</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{deployStats.total}}</text>
        <text class="stats-label">总计</text>
      </view>
    </view>
    
    <!-- 进度条 -->
    <view class="progress-section">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{(deployStats.deployed / deployStats.total) * 100}}%"></view>
      </view>
      <text class="progress-text">{{deployStats.deployed}}/{{deployStats.total}} 完成</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="action-btn primary" bindtap="checkAllFunctions" disabled="{{checking}}">
      {{checking ? '检查中...' : '刷新状态'}}
    </button>
    
    <view class="action-row">
      <button class="action-btn secondary" bindtap="toggleAutoCheck">
        {{autoCheck ? '关闭自动检查' : '开启自动检查'}}
      </button>
      <button class="action-btn secondary" bindtap="onViewGuide">
        部署指南
      </button>
    </view>
  </view>

  <!-- 云函数列表 -->
  <view class="functions-section">
    <!-- 核心功能 -->
    <view class="function-group">
      <view class="group-header">
        <text class="group-title">🔥 核心功能 (优先级1)</text>
        <text class="group-desc">必须先部署这些功能</text>
      </view>
      <view class="function-list">
        <view wx:for="{{cloudFunctions}}" wx:key="name" wx:if="{{item.priority === 1}}"
              class="function-item {{item.status}}">
          <view class="function-header" bindtap="onViewDetails" data-index="{{index}}">
            <text class="function-icon">{{item.status === 'deployed' ? '✅' : item.status === 'failed' ? '❌' : item.status === 'checking' ? '🔄' : '⏳'}}</text>
            <view class="function-info">
              <text class="function-name">{{item.name}}</text>
              <text class="function-desc">{{item.description}}</text>
            </view>
            <view class="function-status">
              <text class="status-text">{{item.status === 'deployed' ? '已部署' : item.status === 'failed' ? '失败' : item.status === 'checking' ? '检查中' : '待部署'}}</text>
            </view>
          </view>
          
          <view class="function-actions">
            <button class="action-btn-small" data-index="{{index}}" bindtap="checkSingleFunction">
              检查
            </button>
            <button class="action-btn-small" data-index="{{index}}" bindtap="onCopyFunctionName">
              复制名称
            </button>
          </view>
          
          <view wx:if="{{item.error}}" class="function-error">
            <text>错误: {{item.error}}</text>
          </view>
          
          <view wx:if="{{item.lastCheck}}" class="function-time">
            <text>最后检查: {{item.lastCheck}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 基础功能 -->
    <view class="function-group">
      <view class="group-header">
        <text class="group-title">📱 基础功能 (优先级2)</text>
        <text class="group-desc">核心功能部署完成后部署</text>
      </view>
      <view class="function-list">
        <view wx:for="{{cloudFunctions}}" wx:key="name" wx:if="{{item.priority === 2}}"
              class="function-item {{item.status}}">
          <view class="function-header" bindtap="onViewDetails" data-index="{{index}}">
            <text class="function-icon">{{item.status === 'deployed' ? '✅' : item.status === 'failed' ? '❌' : item.status === 'checking' ? '🔄' : '⏳'}}</text>
            <view class="function-info">
              <text class="function-name">{{item.name}}</text>
              <text class="function-desc">{{item.description}}</text>
            </view>
            <view class="function-status">
              <text class="status-text">{{item.status === 'deployed' ? '已部署' : item.status === 'failed' ? '失败' : item.status === 'checking' ? '检查中' : '待部署'}}</text>
            </view>
          </view>
          
          <view class="function-actions">
            <button class="action-btn-small" data-index="{{index}}" bindtap="checkSingleFunction">
              检查
            </button>
            <button class="action-btn-small" data-index="{{index}}" bindtap="onCopyFunctionName">
              复制名称
            </button>
          </view>
          
          <view wx:if="{{item.error}}" class="function-error">
            <text>错误: {{item.error}}</text>
          </view>
          
          <view wx:if="{{item.lastCheck}}" class="function-time">
            <text>最后检查: {{item.lastCheck}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 高级功能 -->
    <view class="function-group">
      <view class="group-header">
        <text class="group-title">🎯 高级功能 (优先级3)</text>
        <text class="group-desc">可选功能，最后部署</text>
      </view>
      <view class="function-list">
        <view wx:for="{{cloudFunctions}}" wx:key="name" wx:if="{{item.priority === 3}}"
              class="function-item {{item.status}}">
          <view class="function-header" bindtap="onViewDetails" data-index="{{index}}">
            <text class="function-icon">{{item.status === 'deployed' ? '✅' : item.status === 'failed' ? '❌' : item.status === 'checking' ? '🔄' : '⏳'}}</text>
            <view class="function-info">
              <text class="function-name">{{item.name}}</text>
              <text class="function-desc">{{item.description}}</text>
            </view>
            <view class="function-status">
              <text class="status-text">{{item.status === 'deployed' ? '已部署' : item.status === 'failed' ? '失败' : item.status === 'checking' ? '检查中' : '待部署'}}</text>
            </view>
          </view>
          
          <view class="function-actions">
            <button class="action-btn-small" data-index="{{index}}" bindtap="checkSingleFunction">
              检查
            </button>
            <button class="action-btn-small" data-index="{{index}}" bindtap="onCopyFunctionName">
              复制名称
            </button>
          </view>
          
          <view wx:if="{{item.error}}" class="function-error">
            <text>错误: {{item.error}}</text>
          </view>
          
          <view wx:if="{{item.lastCheck}}" class="function-time">
            <text>最后检查: {{item.lastCheck}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 部署提示 -->
  <view class="deploy-tips">
    <view class="tips-title">💡 部署提示</view>
    <view class="tips-content">
      <text>1. 按优先级顺序部署：核心功能 → 基础功能 → 高级功能</text>
      <text>2. 右键点击云函数文件夹，选择"上传并部署：云端安装依赖"</text>
      <text>3. 每部署一个函数后，点击"检查"按钮验证状态</text>
      <text>4. 所有核心功能部署完成后，运行数据库初始化</text>
    </view>
  </view>
</view>
