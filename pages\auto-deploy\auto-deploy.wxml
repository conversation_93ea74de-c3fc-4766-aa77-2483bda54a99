<!--pages/auto-deploy/auto-deploy.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">🚀 一键自动部署</text>
    <text class="page-desc">自动完成数据库初始化和功能验证</text>
  </view>

  <!-- 环境信息 -->
  <view class="env-info">
    <view class="env-title">🌍 环境信息</view>
    <view wx:if="{{envConfig}}" class="env-details">
      <view class="env-item">
        <text class="env-label">环境:</text>
        <text class="env-value">{{envConfig.currentEnv}}</text>
      </view>
      <view class="env-item">
        <text class="env-label">云环境ID:</text>
        <text class="env-value">{{envConfig.cloudEnvId}}</text>
      </view>
    </view>
  </view>

  <!-- 部署按钮 -->
  <view class="deploy-section">
    <button class="deploy-btn" 
            bindtap="onStartDeploy" 
            disabled="{{deploying}}"
            loading="{{deploying}}">
      {{deploying ? '部署中...' : '开始自动部署'}}
    </button>
    
    <view wx:if="{{deployComplete}}" class="redeploy-section">
      <button class="redeploy-btn" bindtap="onRedeploy">
        重新部署
      </button>
    </view>
  </view>

  <!-- 部署完成提示 -->
  <view wx:if="{{deployComplete}}" class="deploy-success">
    <text>🎉 自动部署完成！您的小程序已准备就绪</text>
  </view>

  <!-- 部署步骤 -->
  <view class="deploy-steps">
    <view class="steps-title">📋 部署步骤</view>
    <view class="steps-list">
      <view wx:for="{{deploySteps}}" wx:key="id" 
            class="step-item {{item.status}} {{index <= currentStep ? 'active' : ''}}">
        <view class="step-header">
          <view class="step-icon">
            <text>{{item.status === 'success' ? '✅' : item.status === 'failed' ? '❌' : item.status === 'running' ? '🔄' : '⏳'}}</text>
          </view>
          <view class="step-content">
            <text class="step-name">{{item.name}}</text>
            <text class="step-desc">{{item.description}}</text>
          </view>
          <view class="step-number">{{index + 1}}</view>
        </view>
        
        <view wx:if="{{item.status === 'running'}}" class="step-progress">
          <text>执行中...</text>
        </view>
        
        <view wx:if="{{item.result}}" class="step-result">
          <text class="result-message">{{item.result.message}}</text>
        </view>
        
        <view wx:if="{{item.error}}" class="step-error">
          <text class="error-message">{{item.error}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 部署结果 -->
  <view wx:if="{{deployResults.length > 0}}" class="deploy-results">
    <view class="results-title">📊 部署结果</view>
    <view class="results-list">
      <view wx:for="{{deployResults}}" wx:key="index" 
            class="result-item {{item.success ? 'success' : 'failed'}}">
        <view class="result-header">
          <text class="result-icon">{{item.success ? '✅' : '❌'}}</text>
          <text class="result-step">{{item.step}}</text>
        </view>
        <view class="result-message">
          <text>{{item.message}}</text>
        </view>
        <view wx:if="{{item.details}}" class="result-actions">
          <button class="details-btn" 
                  data-index="{{index}}" 
                  bindtap="onViewDetails">
            查看详情
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 部署说明 -->
  <view class="deploy-info">
    <view class="info-title">ℹ️ 部署说明</view>
    <view class="info-content">
      <text>1. 自动部署将完成数据库初始化</text>
      <text>2. 验证云函数是否正确部署</text>
      <text>3. 测试核心API功能</text>
      <text>4. 确保所有功能正常工作</text>
      <text>5. 如遇问题，请检查云函数部署状态</text>
    </view>
  </view>

  <!-- 手动部署提示 -->
  <view class="manual-deploy">
    <view class="manual-title">🔧 手动部署步骤</view>
    <view class="manual-content">
      <text>如果自动部署失败，请手动执行：</text>
      <text>1. 右键 cloudfunctions 文件夹</text>
      <text>2. 选择"上传并部署：云端安装依赖"</text>
      <text>3. 等待所有云函数部署完成</text>
      <text>4. 重新运行自动部署</text>
    </view>
  </view>
</view>
