# 🚀 云函数部署检查清单

## 云环境配置
- **环境ID**: `cloud1-8gm001v7fd56ff43`
- **环境类型**: 开发环境
- **配置状态**: ✅ 已配置

## 需要部署的云函数列表

### 📋 核心功能云函数 (必须部署)

#### 1. ✅ 用户管理
- [ ] `login` - 用户登录
- [ ] `getUserInfo` - 获取用户信息
- [ ] `updateUserInfo` - 更新用户信息

#### 2. ✅ 分类管理
- [ ] `getCategories` - 获取分类数据

#### 3. ✅ 资料管理
- [ ] `getMaterials` - 获取资料列表
- [ ] `getMaterialDetail` - 获取资料详情
- [ ] `getRecommendMaterials` - 获取推荐资料
- [ ] `searchMaterials` - 搜索资料

#### 4. ✅ 下载管理
- [ ] `downloadMaterial` - 下载资料
- [ ] `getMyDownloads` - 获取我的下载

#### 5. ✅ 收藏管理
- [ ] `manageFavorite` - 管理收藏
- [ ] `getMyFavorites` - 获取我的收藏

#### 6. ✅ 积分系统
- [ ] `earnPointsByAd` - 观看广告获取积分
- [ ] `earnPointsByShare` - 分享获取积分
- [ ] `earnPointsByCheckin` - 签到获取积分
- [ ] `getPointsLog` - 获取积分记录

### 🎯 高级功能云函数 (推荐部署)

#### 7. ✅ 分享奖励系统
- [ ] `generateShareCode` - 生成分享码
- [ ] `handleShareInvite` - 处理分享邀请
- [ ] `getShareStats` - 获取分享统计
- [ ] `recordShareClick` - 记录分享点击

#### 8. ✅ 文件上传系统
- [ ] `uploadMaterial` - 文件上传处理
- [ ] `validateFile` - 文件验证

#### 9. ✅ 管理后台系统
- [ ] `checkAdminPermission` - 检查管理员权限
- [ ] `getAdminStats` - 获取管理统计
- [ ] `getPendingMaterials` - 获取待审核资料
- [ ] `approveMaterial` - 审核资料
- [ ] `getUsers` - 获取用户列表
- [ ] `manageUser` - 管理用户

#### 10. ✅ 运营支持系统
- [ ] `submitFeedback` - 提交用户反馈
- [ ] `getAnnouncements` - 获取公告列表

## 数据库集合检查清单

### 📊 需要创建的数据库集合

#### 核心集合
- [ ] `users` - 用户信息表
- [ ] `categories` - 分类信息表
- [ ] `materials` - 资料信息表
- [ ] `config` - 系统配置表

#### 功能集合
- [ ] `user_downloads` - 用户下载记录表
- [ ] `user_favorites` - 用户收藏记录表
- [ ] `points_log` - 积分记录表
- [ ] `share_records` - 分享记录表
- [ ] `share_click_log` - 分享点击日志表
- [ ] `upload_log` - 文件上传日志表
- [ ] `feedback` - 用户反馈表
- [ ] `announcements` - 公告表
- [ ] `user_announcement_log` - 用户公告阅读日志表

## 部署步骤

### 1. 📁 云函数部署
```bash
# 在微信开发者工具中
# 1. 右键点击 cloudfunctions 文件夹
# 2. 选择 "上传并部署：云端安装依赖"
# 3. 或者逐个部署每个云函数
```

### 2. 🗄️ 数据库初始化
```javascript
// 在云开发控制台中执行
// 1. 创建所需的数据库集合
// 2. 导入初始数据 (database/init-data.js)
// 3. 设置数据库权限
```

### 3. 🔧 权限配置
```javascript
// 数据库权限设置
{
  "read": true,
  "write": "auth.openid == resource.user_openid"
}
```

## 测试验证

### 🧪 功能测试清单
- [ ] 用户登录注册
- [ ] 分类数据加载
- [ ] 资料搜索和浏览
- [ ] 资料下载功能
- [ ] 收藏功能
- [ ] 积分系统
- [ ] 分享功能
- [ ] 文件上传
- [ ] 管理后台
- [ ] 反馈系统

### 📱 页面测试清单
- [ ] 首页加载正常
- [ ] 分类页面正常
- [ ] 搜索功能正常
- [ ] 个人中心正常
- [ ] 下载记录正常
- [ ] 收藏记录正常
- [ ] 积分明细正常
- [ ] 分享管理正常

## 常见问题解决

### ❓ 云函数调用失败
1. 检查云环境ID是否正确
2. 确认云函数已正确部署
3. 检查云函数代码是否有语法错误
4. 查看云函数日志排查问题

### ❓ 数据库操作失败
1. 检查数据库集合是否已创建
2. 确认数据库权限设置正确
3. 检查数据格式是否符合要求

### ❓ 文件上传失败
1. 检查云存储权限设置
2. 确认文件大小和格式限制
3. 检查网络连接状态

## 部署完成检查

### ✅ 部署成功标志
- [ ] 所有云函数部署成功
- [ ] 数据库集合创建完成
- [ ] 初始数据导入成功
- [ ] 权限配置正确
- [ ] 功能测试通过
- [ ] 页面加载正常
- [ ] 错误处理正常

### 🎉 上线准备
- [ ] 性能测试通过
- [ ] 安全检查完成
- [ ] 用户体验测试通过
- [ ] 文档更新完成
- [ ] 监控配置完成

---

**注意事项：**
1. 请按照顺序部署云函数，确保依赖关系正确
2. 数据库权限设置要谨慎，避免数据泄露
3. 定期备份重要数据
4. 监控云函数调用量和费用
5. 及时更新云函数代码和依赖
