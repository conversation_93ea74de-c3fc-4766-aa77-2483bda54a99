/* pages/test-runner/test-runner.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* 控制面板 */
.control-panel {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.panel-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

/* 测试类型选择 */
.test-types {
  margin-bottom: 30rpx;
}

.test-type-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.test-type-name {
  margin-left: 20rpx;
  font-size: 32rpx;
  color: #333;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  background-color: #f0f0f0;
  color: #666;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 32rpx;
  border: none;
}

.action-btn.primary {
  background-color: #FF6B35;
  color: white;
}

.action-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

/* 测试结果 */
.test-results {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.results-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

/* 统计信息 */
.results-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.summary-item {
  background-color: #f8f8f8;
  border-radius: 15rpx;
  padding: 25rpx;
  text-align: center;
}

.summary-item.success {
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
}

.summary-item.error {
  background-color: #fff2f0;
  border: 1rpx solid #ffccc7;
}

.summary-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.summary-value {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.summary-item.success .summary-value {
  color: #52c41a;
}

.summary-item.error .summary-value {
  color: #ff4d4f;
}

/* 详细结果 */
.results-details {
  margin-bottom: 30rpx;
}

.result-item {
  background-color: #f8f8f8;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 15rpx;
  border-left: 4rpx solid #ddd;
}

.result-item.passed {
  border-left-color: #52c41a;
  background-color: #f6ffed;
}

.result-item.failed {
  border-left-color: #ff4d4f;
  background-color: #fff2f0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.result-status {
  font-size: 32rpx;
}

.result-meta {
  display: flex;
  gap: 20rpx;
  margin-bottom: 10rpx;
}

.result-type,
.result-duration {
  font-size: 24rpx;
  color: #666;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.result-error {
  background-color: rgba(255, 77, 79, 0.1);
  border-radius: 10rpx;
  padding: 15rpx;
  margin-top: 10rpx;
}

.result-error text {
  font-size: 24rpx;
  color: #ff4d4f;
  word-break: break-all;
}

/* 导出按钮 */
.export-section {
  text-align: center;
}

.export-btn {
  background-color: #1890ff;
  color: white;
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 32rpx;
  border: none;
}

/* 测试日志 */
.test-logs {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.logs-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.clear-btn {
  background-color: #f0f0f0;
  color: #666;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  border: none;
}

.logs-content {
  height: 400rpx;
  background-color: #1e1e1e;
  border-radius: 15rpx;
  padding: 20rpx;
}

.log-item {
  display: flex;
  margin-bottom: 10rpx;
  font-family: 'Courier New', monospace;
}

.log-time {
  color: #888;
  font-size: 24rpx;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.log-message {
  color: #fff;
  font-size: 24rpx;
  flex: 1;
  word-break: break-all;
}

.empty-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #666;
  font-size: 28rpx;
}

/* 测试数据 */
.test-data {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.data-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.data-list {
  height: 300rpx;
}

.data-item {
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 10rpx;
}

.data-item .data-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.data-desc {
  font-size: 24rpx;
  color: #666;
}
