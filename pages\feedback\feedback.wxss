/* pages/feedback/feedback.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  margin-bottom: 20rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
}

/* 区块标题 */
.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

/* 反馈类型 */
.feedback-types {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.types-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 15rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.type-item.selected {
  border-color: #FF6B35;
  background-color: #fff7f0;
}

.type-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 15rpx;
}

.type-name {
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

.type-item.selected .type-name {
  color: #FF6B35;
  font-weight: 600;
}

/* 反馈内容 */
.feedback-content {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
  position: relative;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.form-input {
  width: 100%;
  padding: 25rpx;
  background-color: #f8f8f8;
  border-radius: 15rpx;
  font-size: 30rpx;
  border: none;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 25rpx;
  background-color: #f8f8f8;
  border-radius: 15rpx;
  font-size: 30rpx;
  border: none;
  line-height: 1.6;
}

.char-count {
  position: absolute;
  right: 15rpx;
  bottom: 15rpx;
  font-size: 24rpx;
  color: #999;
}

.form-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 图片上传 */
.image-upload {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 15rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4d4f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.add-image-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.add-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 15rpx;
  opacity: 0.6;
}

.add-text {
  font-size: 24rpx;
  color: #999;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 提交区域 */
.submit-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.submit-btn {
  width: 100%;
  background-color: #FF6B35;
  color: white;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 36rpx;
  border: none;
  margin-bottom: 20rpx;
}

.submit-btn[disabled] {
  background-color: #ccc;
}

.submit-tip {
  font-size: 24rpx;
  color: #999;
}
