// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { page = 1, limit = 10, type = 'all' } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    // 构建查询条件
    let whereCondition = {
      is_active: true,
      publish_time: _.lte(new Date())
    }

    // 根据类型筛选
    if (type !== 'all') {
      whereCondition.type = type
    }

    // 查询公告列表
    const skip = (page - 1) * limit
    const result = await db.collection('announcements')
      .where(whereCondition)
      .orderBy('is_pinned', 'desc')
      .orderBy('publish_time', 'desc')
      .skip(skip)
      .limit(limit)
      .get()

    // 获取总数
    const countResult = await db.collection('announcements')
      .where(whereCondition)
      .count()

    // 处理公告数据
    const announcements = result.data.map(announcement => {
      return {
        ...announcement,
        // 判断是否为新公告（7天内）
        isNew: isNewAnnouncement(announcement.publish_time),
        // 判断用户是否已读
        isRead: checkUserRead(announcement, openid)
      }
    })

    // 如果用户已登录，记录阅读状态
    if (openid && announcements.length > 0) {
      try {
        await recordUserView(openid, announcements.map(a => a._id))
      } catch (error) {
        console.error('记录用户阅读状态失败:', error)
      }
    }

    return {
      success: true,
      data: {
        announcements,
        pagination: {
          page,
          limit,
          total: countResult.total,
          hasMore: skip + announcements.length < countResult.total
        }
      }
    }

  } catch (error) {
    console.error('获取公告失败:', error)
    return {
      success: false,
      message: '获取公告失败'
    }
  }
}

// 判断是否为新公告
function isNewAnnouncement(publishTime) {
  const now = new Date()
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  return new Date(publishTime) > sevenDaysAgo
}

// 检查用户是否已读
function checkUserRead(announcement, openid) {
  if (!openid || !announcement.read_users) return false
  return announcement.read_users.includes(openid)
}

// 记录用户查看
async function recordUserView(openid, announcementIds) {
  try {
    const now = new Date()
    
    // 批量更新公告的阅读用户列表
    const updatePromises = announcementIds.map(async (announcementId) => {
      try {
        // 检查用户是否已在阅读列表中
        const announcement = await db.collection('announcements')
          .doc(announcementId)
          .get()
        
        if (announcement.data && announcement.data.read_users) {
          if (!announcement.data.read_users.includes(openid)) {
            // 添加用户到阅读列表
            await db.collection('announcements')
              .doc(announcementId)
              .update({
                data: {
                  read_users: _.push(openid),
                  read_count: _.inc(1)
                }
              })
          }
        } else {
          // 初始化阅读列表
          await db.collection('announcements')
            .doc(announcementId)
            .update({
              data: {
                read_users: [openid],
                read_count: 1
              }
            })
        }
      } catch (error) {
        console.error(`更新公告 ${announcementId} 阅读状态失败:`, error)
      }
    })
    
    await Promise.all(updatePromises)
    
    // 记录用户阅读日志
    await db.collection('user_announcement_log').add({
      data: {
        user_openid: openid,
        announcement_ids: announcementIds,
        view_time: now,
        createTime: now
      }
    })
    
  } catch (error) {
    console.error('记录用户查看失败:', error)
    throw error
  }
}
