// pages/profile/profile.js
const { userAPI, pointsAPI } = require('../../utils/api.js')
const { showError, showSuccess, showConfirm } = require('../../utils/util.js')

Page({
  data: {
    userInfo: null,
    isLogin: false,
    loading: false
  },

  onLoad() {
    this.checkLoginStatus()
  },

  onShow() {
    if (this.data.isLogin) {
      this.loadUserInfo()
    }
  },

  onPullDownRefresh() {
    if (this.data.isLogin) {
      this.loadUserInfo().finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp()
    const userInfo = app.getUserInfo()
    this.setData({
      userInfo,
      isLogin: app.globalData.isLogin
    })
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      this.setData({ loading: true })
      
      const app = getApp()
      const userInfo = await userAPI.getUserInfo()
      
      // 更新全局用户信息
      app.globalData.userInfo = userInfo
      app.globalData.isLogin = true
      wx.setStorageSync('userInfo', userInfo)
      
      this.setData({
        userInfo,
        isLogin: true
      })
    } catch (error) {
      console.error('加载用户信息失败:', error)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 用户登录
  async onLogin() {
    try {
      const app = getApp()
      const userInfo = await app.login()
      
      this.setData({
        userInfo,
        isLogin: true
      })
      
      showSuccess('登录成功')
    } catch (error) {
      console.error('登录失败:', error)
    }
  },

  // 获取用户信息授权
  async onGetUserProfile() {
    try {
      const res = await new Promise((resolve, reject) => {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: resolve,
          fail: reject
        })
      })

      // 更新用户信息
      const updateData = {
        nickName: res.userInfo.nickName,
        avatarUrl: res.userInfo.avatarUrl
      }

      await userAPI.updateUserInfo(updateData)
      
      // 更新本地用户信息
      const app = getApp()
      const currentUserInfo = app.getUserInfo()
      const newUserInfo = { ...currentUserInfo, ...updateData }
      
      app.globalData.userInfo = newUserInfo
      wx.setStorageSync('userInfo', newUserInfo)
      
      this.setData({
        userInfo: newUserInfo
      })
      
      showSuccess('资料更新成功')
    } catch (error) {
      console.error('获取用户信息失败:', error)
      showError('获取用户信息失败')
    }
  },

  // 我的下载
  onMyDownloads() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }
    
    wx.navigateTo({
      url: '/pages/my-downloads/my-downloads'
    })
  },

  // 我的收藏
  onMyFavorites() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }

    wx.navigateTo({
      url: '/pages/my-favorites/my-favorites'
    })
  },

  // 积分明细
  onPointsDetail() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }

    wx.navigateTo({
      url: '/pages/points-detail/points-detail'
    })
  },

  // 积分明细
  onPointsDetail() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }

    wx.navigateTo({
      url: '/pages/points-detail/points-detail'
    })
  },

  // 意见反馈
  onFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    })
  },

  // 去赚积分
  async onEarnPoints() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }

    const options = ['分享给好友', '观看广告', '每日签到']
    const result = await new Promise((resolve) => {
      wx.showActionSheet({
        itemList: options,
        success: (res) => resolve(res.tapIndex),
        fail: () => resolve(-1)
      })
    })

    switch (result) {
      case 0:
        this.onShareApp()
        break
      case 1:
        this.onWatchAd()
        break
      case 2:
        this.onDailyCheckin()
        break
    }
  },

  // 分享管理
  onShareManage() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }

    wx.navigateTo({
      url: '/pages/share-manage/share-manage'
    })
  },

  // 观看广告
  async onWatchAd() {
    try {
      // 检查是否开启广告功能
      const config = await configAPI.getConfig()
      if (!config.enable_ad_reward) {
        showError('广告功能暂未开放')
        return
      }

      // 创建激励视频广告实例
      const videoAd = wx.createRewardedVideoAd({
        adUnitId: 'your-ad-unit-id' // 需要替换为实际的广告位ID
      })

      // 监听广告播放完成
      videoAd.onClose((res) => {
        if (res && res.isEnded) {
          this.handleAdReward()
        } else {
          showError('请完整观看广告才能获得奖励')
        }
      })

      // 显示广告
      await videoAd.show()
    } catch (error) {
      console.error('观看广告失败:', error)
      showError('广告加载失败，请稍后重试')
    }
  },

  // 处理广告奖励
  async handleAdReward() {
    try {
      const result = await pointsAPI.earnPointsByAd()
      showSuccess(`恭喜获得 ${result.points} 积分`)
      
      // 更新用户积分
      const app = getApp()
      app.updateUserPoints(result.newBalance)
      
      this.setData({
        'userInfo.points': result.newBalance
      })
    } catch (error) {
      console.error('获取广告奖励失败:', error)
    }
  },

  // 每日签到
  async onDailyCheckin() {
    try {
      const result = await pointsAPI.earnPointsByCheckin()
      showSuccess(`签到成功，获得 ${result.points} 积分`)
      
      // 更新用户积分
      const app = getApp()
      app.updateUserPoints(result.newBalance)
      
      this.setData({
        'userInfo.points': result.newBalance
      })
    } catch (error) {
      console.error('签到失败:', error)
    }
  },

  // 联系客服
  onContactService() {
    wx.openCustomerServiceChat({
      extInfo: { url: 'https://work.weixin.qq.com/...' }, // 需要配置实际的客服链接
      corpId: 'your-corp-id', // 需要配置实际的企业ID
      success() {
        console.log('打开客服会话成功')
      },
      fail(error) {
        console.error('打开客服会话失败:', error)
        showError('客服功能暂不可用')
      }
    })
  },

  // 帮助与反馈
  onHelpFeedback() {
    wx.navigateTo({
      url: '/pages/help/help'
    })
  },

  // 显示登录提示
  async showLoginTip() {
    const confirm = await showConfirm('请先登录后使用此功能', '提示')
    if (confirm) {
      this.onLogin()
    }
  },

  // 分享页面
  onShareAppMessage() {
    const userInfo = this.data.userInfo
    
    return {
      title: '小学教辅资料 - 海量优质学习资源',
      path: `/pages/home/<USER>''}` : ''}`,
      imageUrl: '/images/share-cover.jpg'
    }
  }
})
