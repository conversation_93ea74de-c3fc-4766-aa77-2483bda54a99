// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { 
    type, 
    title, 
    description, 
    contact, 
    images = [], 
    userInfo, 
    deviceInfo, 
    timestamp 
  } = event
  
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    // 验证必填字段
    if (!type || !title || !description) {
      return {
        success: false,
        message: '缺少必填字段'
      }
    }

    // 验证反馈类型
    const validTypes = ['bug', 'feature', 'content', 'other']
    if (!validTypes.includes(type)) {
      return {
        success: false,
        message: '无效的反馈类型'
      }
    }

    // 验证内容长度
    if (title.length > 50 || description.length > 500) {
      return {
        success: false,
        message: '内容长度超出限制'
      }
    }

    // 检查用户是否存在
    let user = null
    if (openid) {
      const userResult = await db.collection('users')
        .where({ _openid: openid })
        .get()
      
      if (userResult.data.length > 0) {
        user = userResult.data[0]
      }
    }

    // 创建反馈记录
    const feedbackData = {
      type,
      title: title.trim(),
      description: description.trim(),
      contact: contact ? contact.trim() : '',
      images: images || [],
      
      // 用户信息
      user_openid: openid,
      user_nickname: userInfo?.nickName || user?.nickName || '匿名用户',
      user_avatar: userInfo?.avatarUrl || user?.avatarUrl || '',
      
      // 设备信息
      device_info: deviceInfo || {},
      
      // 状态信息
      status: 'pending', // pending, processing, resolved, closed
      priority: getPriority(type),
      
      // 时间信息
      submit_time: timestamp || new Date(),
      createTime: new Date(),
      
      // 处理信息
      assigned_to: null,
      response: '',
      resolve_time: null,
      
      // 统计信息
      view_count: 0,
      like_count: 0
    }

    // 保存到数据库
    const result = await db.collection('feedback').add({
      data: feedbackData
    })

    if (!result._id) {
      throw new Error('保存反馈失败')
    }

    // 发送通知给管理员（可选）
    try {
      await notifyAdmins(feedbackData, result._id)
    } catch (notifyError) {
      console.error('发送管理员通知失败:', notifyError)
      // 通知失败不影响反馈提交
    }

    // 更新用户反馈统计
    if (user) {
      try {
        await updateUserFeedbackStats(user._id)
      } catch (statsError) {
        console.error('更新用户统计失败:', statsError)
      }
    }

    return {
      success: true,
      message: '反馈提交成功',
      data: {
        feedbackId: result._id,
        status: 'pending'
      }
    }

  } catch (error) {
    console.error('提交反馈失败:', error)
    return {
      success: false,
      message: '提交反馈失败，请稍后重试'
    }
  }
}

// 获取反馈优先级
function getPriority(type) {
  const priorityMap = {
    'bug': 'high',      // 问题反馈 - 高优先级
    'feature': 'medium', // 功能建议 - 中优先级
    'content': 'medium', // 内容问题 - 中优先级
    'other': 'low'      // 其他 - 低优先级
  }
  
  return priorityMap[type] || 'low'
}

// 通知管理员
async function notifyAdmins(feedbackData, feedbackId) {
  try {
    // 这里可以实现发送邮件、短信或推送通知给管理员
    // 由于微信小程序的限制，这里只是记录日志
    console.log('新反馈通知:', {
      feedbackId,
      type: feedbackData.type,
      title: feedbackData.title,
      priority: feedbackData.priority,
      user: feedbackData.user_nickname
    })
    
    // 可以调用第三方服务发送通知
    // await sendEmailNotification(feedbackData, feedbackId)
    // await sendWechatNotification(feedbackData, feedbackId)
    
  } catch (error) {
    console.error('发送通知失败:', error)
    throw error
  }
}

// 更新用户反馈统计
async function updateUserFeedbackStats(userId) {
  try {
    const now = new Date()
    
    // 获取用户当前统计
    const userResult = await db.collection('users').doc(userId).get()
    if (!userResult.data) return
    
    const user = userResult.data
    const stats = user.feedback_stats || {
      total_count: 0,
      bug_count: 0,
      feature_count: 0,
      content_count: 0,
      other_count: 0,
      last_feedback_time: null
    }
    
    // 更新统计
    stats.total_count = (stats.total_count || 0) + 1
    stats.last_feedback_time = now
    
    // 更新用户记录
    await db.collection('users').doc(userId).update({
      data: {
        feedback_stats: stats,
        lastActiveTime: now
      }
    })
    
  } catch (error) {
    console.error('更新用户反馈统计失败:', error)
    throw error
  }
}
