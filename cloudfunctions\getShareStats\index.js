// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    if (!openid) {
      return {
        success: false,
        message: '用户未登录'
      }
    }

    // 获取用户的分享统计数据
    const [shareRecords, rewardStats] = await Promise.all([
      getShareRecords(openid),
      getRewardStats(openid)
    ])

    // 计算统计数据
    const stats = {
      // 分享统计
      totalShares: shareRecords.length,
      activeShares: shareRecords.filter(record => record.is_active).length,
      expiredShares: shareRecords.filter(record => !record.is_active || new Date(record.expire_time) < new Date()).length,
      
      // 邀请统计
      totalInvites: shareRecords.reduce((sum, record) => sum + (record.success_count || 0), 0),
      totalClicks: shareRecords.reduce((sum, record) => sum + (record.click_count || 0), 0),
      
      // 奖励统计
      totalRewards: rewardStats.totalRewards,
      totalRewardPoints: rewardStats.totalRewardPoints,
      
      // 最近的分享记录
      recentShares: shareRecords.slice(0, 10).map(record => ({
        shareCode: record.share_code,
        createTime: record.createTime,
        expireTime: record.expire_time,
        isActive: record.is_active && new Date(record.expire_time) > new Date(),
        clickCount: record.click_count || 0,
        successCount: record.success_count || 0,
        rewardGiven: record.reward_given || false
      }))
    }

    return {
      success: true,
      data: stats
    }

  } catch (error) {
    console.error('获取分享统计失败:', error)
    return {
      success: false,
      message: '获取分享统计失败，请稍后重试'
    }
  }
}

// 获取分享记录
async function getShareRecords(openid) {
  try {
    const result = await db.collection('share_records')
      .where({
        sharer_openid: openid
      })
      .orderBy('createTime', 'desc')
      .get()
    
    return result.data || []
  } catch (error) {
    console.error('获取分享记录失败:', error)
    return []
  }
}

// 获取奖励统计
async function getRewardStats(openid) {
  try {
    const result = await db.collection('points_log')
      .where({
        user_openid: openid,
        type: 'share_reward'
      })
      .get()
    
    const rewards = result.data || []
    
    return {
      totalRewards: rewards.length,
      totalRewardPoints: rewards.reduce((sum, reward) => sum + reward.amount, 0)
    }
  } catch (error) {
    console.error('获取奖励统计失败:', error)
    return {
      totalRewards: 0,
      totalRewardPoints: 0
    }
  }
}
