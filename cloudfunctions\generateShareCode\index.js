// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    if (!openid) {
      return {
        success: false,
        message: '用户未登录'
      }
    }

    // 检查用户是否存在
    const userResult = await db.collection('users')
      .where({
        _openid: openid
      })
      .get()

    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    const user = userResult.data[0]

    // 检查是否已有有效的分享码
    const existingShareResult = await db.collection('share_records')
      .where({
        sharer_openid: openid,
        is_active: true,
        expire_time: db.command.gt(new Date())
      })
      .orderBy('createTime', 'desc')
      .limit(1)
      .get()

    if (existingShareResult.data.length > 0) {
      // 返回现有的分享码
      const existingShare = existingShareResult.data[0]
      return {
        success: true,
        message: '获取分享码成功',
        data: {
          shareCode: existingShare.share_code,
          shareUrl: generateShareUrl(existingShare.share_code),
          expireTime: existingShare.expire_time,
          isNew: false
        }
      }
    }

    // 生成新的分享码
    const shareCode = generateUniqueShareCode()
    const now = new Date()
    const expireTime = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30天后过期

    // 创建分享记录
    const shareResult = await db.collection('share_records')
      .add({
        data: {
          share_code: shareCode,
          sharer_openid: openid,
          sharer_nickname: user.nickName || '用户',
          createTime: now,
          expire_time: expireTime,
          is_active: true,
          reward_given: false,
          click_count: 0,
          success_count: 0
        }
      })

    if (!shareResult._id) {
      throw new Error('创建分享记录失败')
    }

    return {
      success: true,
      message: '生成分享码成功',
      data: {
        shareCode: shareCode,
        shareUrl: generateShareUrl(shareCode),
        expireTime: expireTime,
        isNew: true
      }
    }

  } catch (error) {
    console.error('生成分享码失败:', error)
    return {
      success: false,
      message: '生成分享码失败，请稍后重试'
    }
  }
}

// 生成唯一分享码
function generateUniqueShareCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  
  // 生成8位随机码
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  
  // 添加时间戳后4位确保唯一性
  const timestamp = Date.now().toString()
  result += timestamp.slice(-4)
  
  return result
}

// 生成分享链接
function generateShareUrl(shareCode) {
  // 这里应该是小程序的实际路径
  return `pages/home/<USER>
}
