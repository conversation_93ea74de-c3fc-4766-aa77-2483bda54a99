// 云函数部署状态检查工具
// 在微信小程序中运行此代码来检查云函数部署状态

const cloudFunctions = [
  // 第一批：核心功能
  { name: 'initDatabase', priority: 1, desc: '数据库初始化' },
  { name: 'getCategories', priority: 1, desc: '获取分类数据' },
  { name: 'getRecommendMaterials', priority: 1, desc: '获取推荐资料' },
  { name: 'login', priority: 1, desc: '用户登录' },
  { name: 'searchMaterials', priority: 1, desc: '搜索资料' },
  
  // 第二批：基础功能
  { name: 'getMaterialDetail', priority: 2, desc: '获取资料详情' },
  { name: 'downloadMaterial', priority: 2, desc: '下载资料' },
  { name: 'manageFavorite', priority: 2, desc: '管理收藏' },
  { name: 'getMyDownloads', priority: 2, desc: '获取我的下载' },
  { name: 'getMyFavorites', priority: 2, desc: '获取我的收藏' },
  
  // 第三批：高级功能
  { name: 'generateShareCode', priority: 3, desc: '生成分享码' },
  { name: 'handleShareInvite', priority: 3, desc: '处理分享邀请' },
  { name: 'getShareStats', priority: 3, desc: '获取分享统计' },
  { name: 'recordShareClick', priority: 3, desc: '记录分享点击' },
  { name: 'uploadMaterial', priority: 3, desc: '文件上传' },
  { name: 'validateFile', priority: 3, desc: '文件验证' },
  { name: 'submitFeedback', priority: 3, desc: '提交反馈' },
  { name: 'getAnnouncements', priority: 3, desc: '获取公告' },
  { name: 'checkAdminPermission', priority: 3, desc: '管理员权限检查' },
  { name: 'getAdminStats', priority: 3, desc: '获取管理统计' }
];

// 检查单个云函数
async function checkFunction(functionName) {
  try {
    const result = await wx.cloud.callFunction({
      name: functionName,
      data: { test: true }
    });
    return { success: true, result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 检查所有云函数
async function checkAllFunctions() {
  console.log('🔍 开始检查云函数部署状态...');
  
  const results = {
    total: cloudFunctions.length,
    deployed: 0,
    failed: 0,
    details: []
  };
  
  for (const func of cloudFunctions) {
    console.log(`检查: ${func.name} (${func.desc})`);
    
    const result = await checkFunction(func.name);
    const status = {
      name: func.name,
      desc: func.desc,
      priority: func.priority,
      deployed: result.success,
      error: result.error || null
    };
    
    results.details.push(status);
    
    if (result.success) {
      results.deployed++;
      console.log(`✅ ${func.name} - 部署成功`);
    } else {
      results.failed++;
      console.log(`❌ ${func.name} - 部署失败: ${result.error}`);
    }
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  return results;
}

// 生成部署报告
function generateReport(results) {
  console.log('\n📊 部署状态报告:');
  console.log(`总计: ${results.total} 个云函数`);
  console.log(`已部署: ${results.deployed} 个`);
  console.log(`未部署: ${results.failed} 个`);
  console.log(`部署率: ${Math.round(results.deployed / results.total * 100)}%`);
  
  // 按优先级分组显示
  const priorities = [1, 2, 3];
  priorities.forEach(priority => {
    const funcs = results.details.filter(f => f.priority === priority);
    const deployed = funcs.filter(f => f.deployed).length;
    const priorityName = priority === 1 ? '核心功能' : priority === 2 ? '基础功能' : '高级功能';
    
    console.log(`\n${priorityName} (优先级${priority}): ${deployed}/${funcs.length} 已部署`);
    
    funcs.forEach(func => {
      const status = func.deployed ? '✅' : '❌';
      console.log(`  ${status} ${func.name} - ${func.desc}`);
      if (!func.deployed && func.error) {
        console.log(`     错误: ${func.error}`);
      }
    });
  });
  
  // 生成下一步建议
  console.log('\n🎯 下一步建议:');
  
  if (results.deployed === 0) {
    console.log('❗ 没有云函数部署成功，请检查：');
    console.log('1. 确认云开发服务已开通');
    console.log('2. 确认云环境ID正确');
    console.log('3. 使用微信开发者工具右键部署云函数');
  } else if (results.deployed < 5) {
    console.log('⚠️ 核心功能未完全部署，建议优先部署：');
    const coreNotDeployed = results.details.filter(f => f.priority === 1 && !f.deployed);
    coreNotDeployed.forEach(func => {
      console.log(`   - ${func.name} (${func.desc})`);
    });
  } else if (results.deployed < results.total) {
    console.log('👍 核心功能已部署，可以继续部署其他功能');
    const notDeployed = results.details.filter(f => !f.deployed);
    console.log('未部署的函数:');
    notDeployed.forEach(func => {
      console.log(`   - ${func.name} (${func.desc})`);
    });
  } else {
    console.log('🎉 所有云函数都已成功部署！');
    console.log('可以开始使用小程序的完整功能了。');
  }
  
  return results;
}

// 主函数 - 在小程序中调用
async function checkDeploymentStatus() {
  try {
    console.log('🚀 云函数部署状态检查工具 - 2025年版');
    console.log('环境ID: cloud1-8gm001v7fd56ff43');
    console.log('');
    
    const results = await checkAllFunctions();
    const report = generateReport(results);
    
    return {
      success: true,
      data: report
    };
  } catch (error) {
    console.error('检查过程中出现错误:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 导出函数供小程序页面使用
module.exports = {
  checkDeploymentStatus,
  checkFunction,
  cloudFunctions
};

// 使用示例（在小程序页面中）:
/*
const checker = require('../../deploy/check-functions.js');

Page({
  async onLoad() {
    const result = await checker.checkDeploymentStatus();
    if (result.success) {
      console.log('检查完成', result.data);
    } else {
      console.error('检查失败', result.error);
    }
  }
});
*/
