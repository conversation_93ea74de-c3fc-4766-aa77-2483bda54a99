// pages/share-manage/share-manage.js
const { pointsAPI } = require('../../utils/api.js')
const { showError, showSuccess, showLoading, hideLoading, showConfirm, copyToClipboard } = require('../../utils/util.js')

Page({
  data: {
    // 用户信息
    userInfo: null,
    isLogin: false,
    
    // 分享统计
    shareStats: null,
    
    // 当前分享码
    currentShareCode: '',
    shareUrl: '',
    
    // 加载状态
    loading: true,
    generating: false
  },

  onLoad(options) {
    this.initPage()
  },

  onShow() {
    // 更新用户信息
    this.updateUserInfo()
  },

  onPullDownRefresh() {
    this.initPage().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ loading: true })
      
      const app = getApp()
      if (!app.globalData.isLogin) {
        this.setData({
          isLogin: false,
          loading: false
        })
        return
      }

      // 并行加载数据
      await Promise.all([
        this.loadShareStats(),
        this.generateOrGetShareCode()
      ])

    } catch (error) {
      console.error('页面初始化失败:', error)
      showError('页面加载失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 更新用户信息
  updateUserInfo() {
    const app = getApp()
    this.setData({
      userInfo: app.getUserInfo(),
      isLogin: app.globalData.isLogin
    })
  },

  // 加载分享统计
  async loadShareStats() {
    try {
      const result = await pointsAPI.getShareStats()
      if (result.success) {
        this.setData({
          shareStats: result.data
        })
      }
    } catch (error) {
      console.error('加载分享统计失败:', error)
    }
  },

  // 生成或获取分享码
  async generateOrGetShareCode() {
    try {
      this.setData({ generating: true })
      
      const result = await pointsAPI.generateShareCode()
      if (result.success) {
        this.setData({
          currentShareCode: result.data.shareCode,
          shareUrl: result.data.shareUrl
        })
      } else {
        showError(result.message || '获取分享码失败')
      }
    } catch (error) {
      console.error('获取分享码失败:', error)
      showError('获取分享码失败')
    } finally {
      this.setData({ generating: false })
    }
  },

  // 复制分享码
  async onCopyShareCode() {
    if (!this.data.currentShareCode) {
      showError('分享码不存在')
      return
    }

    const success = await copyToClipboard(this.data.currentShareCode)
    if (success) {
      showSuccess('分享码已复制到剪贴板')
    }
  },

  // 复制分享链接
  async onCopyShareUrl() {
    if (!this.data.shareUrl) {
      showError('分享链接不存在')
      return
    }

    const shareText = `小学教辅资料 - 海量优质学习资源\n邀请码：${this.data.currentShareCode}\n快来一起学习吧！`
    const success = await copyToClipboard(shareText)
    if (success) {
      showSuccess('分享内容已复制到剪贴板')
    }
  },

  // 分享给好友
  onShareToFriend() {
    if (!this.data.currentShareCode) {
      showError('分享码不存在')
      return
    }

    // 触发分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    
    showSuccess('请点击右上角分享给好友')
  },

  // 刷新数据
  async onRefresh() {
    await this.initPage()
    showSuccess('数据已刷新')
  },

  // 查看分享记录详情
  onViewShareDetail(e) {
    const { share } = e.currentTarget.dataset
    
    const detail = `分享码：${share.shareCode}\n创建时间：${this.formatTime(share.createTime)}\n过期时间：${this.formatTime(share.expireTime)}\n点击次数：${share.clickCount}\n成功邀请：${share.successCount}人\n状态：${share.isActive ? '有效' : '已过期'}`
    
    wx.showModal({
      title: '分享详情',
      content: detail,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 去登录
  onGoLogin() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  // 格式化时间
  formatTime(timeStr) {
    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  },

  // 分享页面
  onShareAppMessage() {
    const shareCode = this.data.currentShareCode
    
    return {
      title: '小学教辅资料 - 海量优质学习资源',
      path: `/pages/home/<USER>
      imageUrl: '/images/share-cover.svg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '小学教辅资料 - 海量优质学习资源',
      imageUrl: '/images/share-cover.svg'
    }
  }
})
