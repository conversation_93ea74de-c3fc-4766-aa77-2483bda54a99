// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 管理员openid列表（实际项目中应该存储在数据库中）
const ADMIN_OPENIDS = [
  // 在这里添加管理员的openid
  'admin-openid-1',
  'admin-openid-2'
]

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    if (!openid) {
      return {
        success: false,
        message: '用户未登录'
      }
    }

    // 检查是否为管理员
    const isAdmin = ADMIN_OPENIDS.includes(openid)
    
    if (!isAdmin) {
      return {
        success: false,
        message: '权限不足'
      }
    }

    // 获取管理员信息
    const userResult = await db.collection('users')
      .where({
        _openid: openid
      })
      .get()

    let adminInfo = {
      openid,
      isAdmin: true,
      permissions: ['read', 'write', 'delete', 'approve']
    }

    if (userResult.data.length > 0) {
      const user = userResult.data[0]
      adminInfo = {
        ...adminInfo,
        nickName: user.nickName,
        avatarUrl: user.avatarUrl,
        createTime: user.createTime
      }
    }

    return {
      success: true,
      data: adminInfo
    }

  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return {
      success: false,
      message: '权限验证失败'
    }
  }
}
