// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { shareCode } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    if (!shareCode) {
      return {
        success: false,
        message: '分享码不能为空'
      }
    }

    // 查询分享记录
    const shareResult = await db.collection('share_records')
      .where({
        share_code: shareCode,
        is_active: true
      })
      .get()

    if (shareResult.data.length === 0) {
      return {
        success: false,
        message: '无效的分享码'
      }
    }

    const shareRecord = shareResult.data[0]

    // 检查分享码是否过期
    if (new Date(shareRecord.expire_time) < new Date()) {
      // 标记为过期
      await db.collection('share_records')
        .doc(shareRecord._id)
        .update({
          data: {
            is_active: false
          }
        })

      return {
        success: false,
        message: '分享码已过期'
      }
    }

    // 防刷机制：检查同一用户在短时间内的点击次数
    if (openid) {
      const recentClicks = await db.collection('share_click_log')
        .where({
          share_code: shareCode,
          clicker_openid: openid,
          click_time: _.gte(new Date(Date.now() - 60 * 1000)) // 1分钟内
        })
        .count()

      if (recentClicks.total > 3) {
        return {
          success: false,
          message: '点击过于频繁，请稍后再试'
        }
      }
    }

    // 记录点击日志
    const clickLogData = {
      share_code: shareCode,
      sharer_openid: shareRecord.sharer_openid,
      click_time: new Date(),
      ip: event.userIP || 'unknown'
    }

    if (openid) {
      clickLogData.clicker_openid = openid
    }

    await db.collection('share_click_log').add({
      data: clickLogData
    })

    // 更新分享记录的点击次数
    await db.collection('share_records')
      .doc(shareRecord._id)
      .update({
        data: {
          click_count: _.inc(1),
          last_click_time: new Date()
        }
      })

    return {
      success: true,
      message: '记录点击成功',
      data: {
        shareCode: shareCode,
        sharerNickname: shareRecord.sharer_nickname,
        isOwnShare: openid === shareRecord.sharer_openid
      }
    }

  } catch (error) {
    console.error('记录分享点击失败:', error)
    return {
      success: false,
      message: '记录分享点击失败，请稍后重试'
    }
  }
}
