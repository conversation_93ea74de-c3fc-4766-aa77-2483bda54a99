// pages/simple-deploy/simple-deploy.js
// 简化版部署页面，不依赖云函数

Page({
  data: {
    envConfig: {
      cloudEnvId: 'cloud1-8gm001v7fd56ff43',
      currentEnv: 'development'
    },
    
    deploySteps: [
      {
        id: 'check-cloud',
        name: '检查云开发环境',
        status: 'pending',
        description: '验证云开发是否正确初始化'
      },
      {
        id: 'check-database',
        name: '检查数据库连接',
        status: 'pending', 
        description: '测试数据库读写权限'
      },
      {
        id: 'create-collections',
        name: '创建数据库集合',
        status: 'pending',
        description: '手动创建必要的数据库集合'
      },
      {
        id: 'init-data',
        name: '初始化数据',
        status: 'pending',
        description: '添加基础分类和配置数据'
      }
    ],
    
    currentStep: -1,
    deploying: false,
    deployComplete: false,
    
    // 手动操作指南
    manualSteps: [
      {
        title: '1. 打开云开发控制台',
        content: '在微信开发者工具中点击"工具" → "云开发"'
      },
      {
        title: '2. 进入数据库管理',
        content: '在控制台左侧点击"数据库" → "集合管理"'
      },
      {
        title: '3. 创建数据库集合',
        content: '点击"添加集合"，创建以下集合：categories, config, users, materials'
      },
      {
        title: '4. 导入初始数据',
        content: '使用下方的"一键初始化数据"功能'
      }
    ]
  },

  onLoad() {
    this.checkEnvironment()
  },

  // 检查环境
  async checkEnvironment() {
    try {
      // 检查云开发是否初始化
      if (!wx.cloud) {
        throw new Error('云开发未初始化')
      }

      // 检查数据库连接
      const db = wx.cloud.database()
      
      wx.showToast({
        title: '环境检查通过',
        icon: 'success'
      })
      
    } catch (error) {
      wx.showModal({
        title: '环境检查失败',
        content: `错误：${error.message}\n\n请确保：\n1. 已开通云开发服务\n2. 环境ID配置正确\n3. 网络连接正常`,
        showCancel: false
      })
    }
  },

  // 开始简化部署
  async onStartSimpleDeploy() {
    this.setData({
      deploying: true,
      currentStep: 0
    })

    try {
      // 执行简化的部署步骤
      await this.executeSimpleSteps()
      
      this.setData({
        deployComplete: true,
        deploying: false
      })

      wx.showToast({
        title: '基础配置完成！',
        icon: 'success'
      })

    } catch (error) {
      this.setData({
        deploying: false
      })
      
      wx.showModal({
        title: '部署失败',
        content: error.message,
        showCancel: false
      })
    }
  },

  // 执行简化步骤
  async executeSimpleSteps() {
    const steps = this.data.deploySteps
    
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i]
      
      // 更新当前步骤
      step.status = 'running'
      this.setData({
        currentStep: i,
        deploySteps: steps
      })

      try {
        switch (step.id) {
          case 'check-cloud':
            await this.checkCloudInit()
            break
          case 'check-database':
            await this.checkDatabase()
            break
          case 'create-collections':
            await this.createCollections()
            break
          case 'init-data':
            await this.initBasicData()
            break
        }

        step.status = 'success'
        this.setData({ deploySteps: steps })
        
        // 添加延迟
        await this.delay(1000)

      } catch (error) {
        step.status = 'failed'
        step.error = error.message
        this.setData({ deploySteps: steps })
        throw error
      }
    }
  },

  // 检查云开发初始化
  async checkCloudInit() {
    if (!wx.cloud) {
      throw new Error('云开发未初始化')
    }
    
    // 尝试获取云环境信息
    try {
      const db = wx.cloud.database()
      return true
    } catch (error) {
      throw new Error('云开发环境连接失败')
    }
  },

  // 检查数据库
  async checkDatabase() {
    try {
      const db = wx.cloud.database()
      
      // 尝试读取一个可能不存在的集合
      await db.collection('test_connection').limit(1).get()
      
      return true
    } catch (error) {
      // 如果是集合不存在的错误，说明数据库连接正常
      if (error.errMsg && error.errMsg.includes('collection not exist')) {
        return true
      }
      throw new Error('数据库连接失败')
    }
  },

  // 创建集合
  async createCollections() {
    const db = wx.cloud.database()
    const collections = ['categories', 'config', 'users', 'materials']
    
    for (const collectionName of collections) {
      try {
        // 尝试在集合中添加一个临时文档来创建集合
        const result = await db.collection(collectionName).add({
          data: {
            _temp: true,
            createTime: new Date()
          }
        })
        
        // 立即删除临时文档
        await db.collection(collectionName).doc(result._id).remove()
        
      } catch (error) {
        // 如果集合已存在，忽略错误
        if (!error.errMsg || !error.errMsg.includes('duplicate')) {
          console.warn(`创建集合 ${collectionName} 时出现警告:`, error)
        }
      }
    }
    
    return true
  },

  // 初始化基础数据
  async initBasicData() {
    const db = wx.cloud.database()
    
    try {
      // 添加基础分类数据
      await db.collection('categories').add({
        data: {
          _id: 'grade',
          name: '年级',
          options: [
            { id: 'grade-1', name: '一年级', is_active: true },
            { id: 'grade-2', name: '二年级', is_active: true },
            { id: 'grade-3', name: '三年级', is_active: true },
            { id: 'grade-4', name: '四年级', is_active: true },
            { id: 'grade-5', name: '五年级', is_active: true },
            { id: 'grade-6', name: '六年级', is_active: true }
          ],
          createTime: new Date()
        }
      })

      // 添加科目分类
      await db.collection('categories').add({
        data: {
          _id: 'subject',
          name: '科目',
          options: [
            { id: 'chinese', name: '语文', is_active: true },
            { id: 'math', name: '数学', is_active: true },
            { id: 'english', name: '英语', is_active: true },
            { id: 'science', name: '科学', is_active: true }
          ],
          createTime: new Date()
        }
      })

      // 添加基础配置
      await db.collection('config').add({
        data: {
          _id: 'app_settings',
          name: '应用设置',
          config: {
            app_name: '小学教辅资料',
            version: '1.0.0',
            maintenance: false
          },
          createTime: new Date()
        }
      })

      return true
    } catch (error) {
      // 如果数据已存在，不算错误
      if (error.errMsg && error.errMsg.includes('duplicate')) {
        return true
      }
      throw new Error('初始化数据失败: ' + error.message)
    }
  },

  // 一键初始化数据
  async onInitData() {
    try {
      wx.showLoading({ title: '初始化中...' })
      
      await this.initBasicData()
      
      wx.hideLoading()
      wx.showToast({
        title: '数据初始化成功',
        icon: 'success'
      })
      
    } catch (error) {
      wx.hideLoading()
      wx.showModal({
        title: '初始化失败',
        content: error.message,
        showCancel: false
      })
    }
  },

  // 测试数据库连接
  async onTestDatabase() {
    try {
      wx.showLoading({ title: '测试中...' })
      
      const db = wx.cloud.database()
      const result = await db.collection('categories').limit(1).get()
      
      wx.hideLoading()
      wx.showModal({
        title: '测试结果',
        content: `数据库连接正常\n找到 ${result.data.length} 条记录`,
        showCancel: false
      })
      
    } catch (error) {
      wx.hideLoading()
      wx.showModal({
        title: '测试失败',
        content: error.message,
        showCancel: false
      })
    }
  },

  // 打开云开发控制台指南
  onOpenConsoleGuide() {
    wx.showModal({
      title: '打开云开发控制台',
      content: '1. 在微信开发者工具顶部菜单点击"工具"\n2. 选择"云开发"\n3. 或点击工具栏的云开发图标 ☁️',
      showCancel: false
    })
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // 获取状态图标
  getStatusIcon(status) {
    switch (status) {
      case 'success':
        return '✅'
      case 'failed':
        return '❌'
      case 'running':
        return '🔄'
      default:
        return '⏳'
    }
  }
})
