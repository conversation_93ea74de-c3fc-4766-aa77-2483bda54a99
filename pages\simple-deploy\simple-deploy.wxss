/* pages/simple-deploy/simple-deploy.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  margin-bottom: 20rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
}

/* 环境信息 */
.env-info {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.env-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.env-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.env-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}

.env-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 快速操作 */
.quick-actions {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  width: 100%;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  border: none;
  margin-bottom: 20rpx;
}

.action-btn.primary {
  background-color: #FF6B35;
  color: white;
  font-weight: 600;
}

.action-btn.secondary {
  background-color: #f0f0f0;
  color: #666;
}

.action-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

.action-row {
  display: flex;
  gap: 20rpx;
}

.action-row .action-btn {
  flex: 1;
  margin-bottom: 0;
}

/* 配置成功提示 */
.deploy-success {
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.deploy-success text {
  display: block;
  font-size: 32rpx;
  color: #52c41a;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.deploy-success text:last-child {
  font-size: 28rpx;
  font-weight: normal;
  margin-bottom: 0;
}

/* 配置步骤 */
.deploy-steps {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.steps-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-item {
  background-color: #f8f8f8;
  border-radius: 15rpx;
  padding: 25rpx;
  border-left: 4rpx solid #ddd;
  transition: all 0.3s ease;
}

.step-item.active {
  border-left-color: #FF6B35;
}

.step-item.success {
  border-left-color: #52c41a;
  background-color: #f6ffed;
}

.step-item.failed {
  border-left-color: #ff4d4f;
  background-color: #fff2f0;
}

.step-item.running {
  border-left-color: #1890ff;
  background-color: #e6f7ff;
}

.step-header {
  display: flex;
  align-items: center;
}

.step-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.step-content {
  flex: 1;
}

.step-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
}

.step-error {
  background-color: rgba(255, 77, 79, 0.1);
  border-radius: 10rpx;
  padding: 15rpx;
  margin-top: 15rpx;
}

.step-error text {
  font-size: 24rpx;
  color: #ff4d4f;
}

/* 手动操作指南 */
.manual-guide {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.guide-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.guide-content {
  margin-bottom: 30rpx;
}

.guide-step {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 15rpx;
}

.guide-step-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.guide-step-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.guide-btn {
  background-color: #1890ff;
  color: white;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 28rpx;
  border: none;
  width: 100%;
}

/* 功能说明和下一步 */
.feature-info,
.next-steps {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-title,
.next-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.info-content,
.next-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-content text,
.next-content text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 动画效果 */
.step-item.running .step-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
