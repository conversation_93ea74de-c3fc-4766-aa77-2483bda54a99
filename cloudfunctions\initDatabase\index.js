// 云函数入口文件 - 数据库初始化
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { action = 'init' } = event
  
  try {
    switch (action) {
      case 'init':
        return await initAllData()
      case 'categories':
        return await initCategories()
      case 'config':
        return await initConfig()
      case 'check':
        return await checkDatabase()
      default:
        return { success: false, message: '未知操作' }
    }
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return {
      success: false,
      message: error.message,
      error: error
    }
  }
}

// 初始化所有数据
async function initAllData() {
  const results = []
  
  try {
    // 1. 初始化分类数据
    const categoriesResult = await initCategories()
    results.push({ step: '分类数据', ...categoriesResult })
    
    // 2. 初始化系统配置
    const configResult = await initConfig()
    results.push({ step: '系统配置', ...configResult })
    
    // 3. 创建其他必要集合
    const collectionsResult = await createCollections()
    results.push({ step: '创建集合', ...collectionsResult })
    
    return {
      success: true,
      message: '数据库初始化完成',
      results
    }
  } catch (error) {
    return {
      success: false,
      message: '数据库初始化失败',
      error: error.message,
      results
    }
  }
}

// 初始化分类数据
async function initCategories() {
  try {
    const categories = [
      {
        _id: 'grade',
        name: '年级',
        type: 'select',
        is_active: true,
        sort_order: 1,
        options: [
          { id: 'grade-1', name: '一年级', is_active: true, sort_order: 1 },
          { id: 'grade-2', name: '二年级', is_active: true, sort_order: 2 },
          { id: 'grade-3', name: '三年级', is_active: true, sort_order: 3 },
          { id: 'grade-4', name: '四年级', is_active: true, sort_order: 4 },
          { id: 'grade-5', name: '五年级', is_active: true, sort_order: 5 },
          { id: 'grade-6', name: '六年级', is_active: true, sort_order: 6 }
        ],
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        _id: 'subject',
        name: '科目',
        type: 'select',
        is_active: true,
        sort_order: 2,
        options: [
          { id: 'chinese', name: '语文', is_active: true, sort_order: 1 },
          { id: 'math', name: '数学', is_active: true, sort_order: 2 },
          { id: 'english', name: '英语', is_active: true, sort_order: 3 },
          { id: 'science', name: '科学', is_active: true, sort_order: 4 },
          { id: 'art', name: '美术', is_active: true, sort_order: 5 },
          { id: 'music', name: '音乐', is_active: true, sort_order: 6 },
          { id: 'pe', name: '体育', is_active: true, sort_order: 7 }
        ],
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        _id: 'semester',
        name: '上下册',
        type: 'select',
        is_active: true,
        sort_order: 3,
        options: [
          { id: 'first', name: '上册', is_active: true, sort_order: 1 },
          { id: 'second', name: '下册', is_active: true, sort_order: 2 },
          { id: 'whole', name: '全册', is_active: true, sort_order: 3 }
        ],
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        _id: 'textbook',
        name: '教材版本',
        type: 'select',
        is_active: true,
        sort_order: 4,
        options: [
          { id: 'renjiao', name: '人教版', is_active: true, sort_order: 1 },
          { id: 'beijing', name: '北师大版', is_active: true, sort_order: 2 },
          { id: 'sujiao', name: '苏教版', is_active: true, sort_order: 3 },
          { id: 'shanghai', name: '沪教版', is_active: true, sort_order: 4 },
          { id: 'yuwen', name: '语文版', is_active: true, sort_order: 5 }
        ],
        createTime: new Date(),
        updateTime: new Date()
      }
    ]

    // 删除现有数据（如果存在）
    try {
      await db.collection('categories').get().then(res => {
        const batch = db.batch()
        res.data.forEach(doc => {
          batch.delete(db.collection('categories').doc(doc._id))
        })
        return batch.commit()
      })
    } catch (error) {
      // 集合可能不存在，忽略错误
    }

    // 插入新数据
    const batch = db.batch()
    categories.forEach(category => {
      batch.add(db.collection('categories').doc(category._id), category)
    })
    
    await batch.commit()

    return {
      success: true,
      message: `成功初始化 ${categories.length} 个分类`
    }
  } catch (error) {
    return {
      success: false,
      message: '分类数据初始化失败',
      error: error.message
    }
  }
}

// 初始化系统配置
async function initConfig() {
  try {
    const configs = [
      {
        _id: 'points_rules',
        name: '积分规则',
        config: {
          register_reward: 100,
          daily_checkin: 10,
          share_reward: 20,
          invite_reward: 50,
          ad_reward: 5,
          download_cost_base: 10
        },
        is_active: true,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        _id: 'app_settings',
        name: '应用设置',
        config: {
          app_name: '小学教辅资料',
          version: '1.0.0',
          maintenance: false,
          announcement: '欢迎使用小学教辅资料小程序！',
          max_download_per_day: 10,
          max_file_size: 50 * 1024 * 1024
        },
        is_active: true,
        createTime: new Date(),
        updateTime: new Date()
      }
    ]

    // 删除现有配置
    try {
      await db.collection('config').get().then(res => {
        const batch = db.batch()
        res.data.forEach(doc => {
          batch.delete(db.collection('config').doc(doc._id))
        })
        return batch.commit()
      })
    } catch (error) {
      // 集合可能不存在，忽略错误
    }

    // 插入新配置
    const batch = db.batch()
    configs.forEach(config => {
      batch.add(db.collection('config').doc(config._id), config)
    })
    
    await batch.commit()

    return {
      success: true,
      message: `成功初始化 ${configs.length} 个配置项`
    }
  } catch (error) {
    return {
      success: false,
      message: '系统配置初始化失败',
      error: error.message
    }
  }
}

// 创建必要的集合
async function createCollections() {
  const collections = [
    'users',
    'materials', 
    'user_downloads',
    'user_favorites',
    'points_log',
    'share_records',
    'share_click_log',
    'upload_log',
    'feedback',
    'announcements',
    'user_announcement_log'
  ]

  const results = []

  for (const collectionName of collections) {
    try {
      // 尝试在集合中添加一个临时文档，如果集合不存在会自动创建
      const tempDoc = await db.collection(collectionName).add({
        data: {
          _temp: true,
          createTime: new Date()
        }
      })

      // 立即删除临时文档
      await db.collection(collectionName).doc(tempDoc._id).remove()

      results.push({
        collection: collectionName,
        success: true,
        message: '集合创建成功'
      })
    } catch (error) {
      results.push({
        collection: collectionName,
        success: false,
        message: error.message
      })
    }
  }

  return {
    success: true,
    message: `处理了 ${collections.length} 个集合`,
    details: results
  }
}

// 检查数据库状态
async function checkDatabase() {
  const collections = [
    'categories',
    'config', 
    'users',
    'materials',
    'user_downloads',
    'user_favorites',
    'points_log'
  ]

  const results = []

  for (const collectionName of collections) {
    try {
      const result = await db.collection(collectionName).count()
      results.push({
        collection: collectionName,
        exists: true,
        count: result.total
      })
    } catch (error) {
      results.push({
        collection: collectionName,
        exists: false,
        error: error.message
      })
    }
  }

  return {
    success: true,
    message: '数据库检查完成',
    collections: results
  }
}
