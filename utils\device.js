/**
 * 设备适配工具类
 * 提供设备信息获取、屏幕适配、兼容性处理等功能
 */

/**
 * 设备信息管理类
 */
class DeviceManager {
  constructor() {
    this.systemInfo = null
    this.safeArea = null
    this.init()
  }

  /**
   * 初始化设备信息
   */
  init() {
    try {
      // 使用新的API替代废弃的wx.getSystemInfoSync
      const windowInfo = wx.getWindowInfo()
      const deviceInfo = wx.getDeviceInfo()
      const appBaseInfo = wx.getAppBaseInfo()

      // 组合系统信息
      this.systemInfo = {
        ...windowInfo,
        ...deviceInfo,
        ...appBaseInfo,
        // 保持向后兼容
        windowWidth: windowInfo.windowWidth,
        windowHeight: windowInfo.windowHeight,
        statusBarHeight: windowInfo.statusBarHeight,
        safeArea: windowInfo.safeArea,
        brand: deviceInfo.brand,
        model: deviceInfo.model,
        system: deviceInfo.system,
        platform: deviceInfo.platform,
        version: appBaseInfo.version,
        SDKVersion: appBaseInfo.SDKVersion
      }

      this.calculateSafeArea()
      this.setGlobalStyles()
    } catch (error) {
      console.error('获取设备信息失败:', error)
      // 降级到旧API
      try {
        this.systemInfo = wx.getSystemInfoSync()
        this.calculateSafeArea()
        this.setGlobalStyles()
      } catch (fallbackError) {
        console.error('降级获取设备信息也失败:', fallbackError)
      }
    }
  }

  /**
   * 计算安全区域
   */
  calculateSafeArea() {
    if (!this.systemInfo) return

    const { windowHeight, windowWidth, safeArea, statusBarHeight } = this.systemInfo

    this.safeArea = {
      top: safeArea ? safeArea.top : statusBarHeight,
      bottom: safeArea ? safeArea.bottom : windowHeight,
      left: safeArea ? safeArea.left : 0,
      right: safeArea ? safeArea.right : windowWidth,
      width: safeArea ? safeArea.width : windowWidth,
      height: safeArea ? safeArea.height : windowHeight - statusBarHeight
    }
  }

  /**
   * 设置全局样式变量
   */
  setGlobalStyles() {
    if (!this.systemInfo || !this.safeArea) return

    const { statusBarHeight, windowHeight, windowWidth } = this.systemInfo
    const { top, bottom } = this.safeArea

    // 设置CSS变量
    try {
      const app = getApp()
      if (app && app.globalData) {
        app.globalData.deviceInfo = {
          statusBarHeight,
          safeAreaTop: top,
          safeAreaBottom: windowHeight - bottom,
          windowHeight,
          windowWidth,
          isIPhoneX: this.isIPhoneX(),
          isAndroid: this.isAndroid(),
          isIOS: this.isIOS()
        }
      }
    } catch (error) {
      // getApp() 在模块初始化时可能不可用，忽略错误
      console.warn('设置全局样式变量时 getApp() 不可用:', error.message)
    }
  }

  /**
   * 获取设备信息
   */
  getSystemInfo() {
    return this.systemInfo
  }

  /**
   * 获取安全区域信息
   */
  getSafeArea() {
    return this.safeArea
  }

  /**
   * 是否为iPhone X系列
   */
  isIPhoneX() {
    if (!this.systemInfo) return false
    
    const { model, platform } = this.systemInfo
    return platform === 'ios' && (
      model.includes('iPhone X') ||
      model.includes('iPhone 11') ||
      model.includes('iPhone 12') ||
      model.includes('iPhone 13') ||
      model.includes('iPhone 14') ||
      model.includes('iPhone 15')
    )
  }

  /**
   * 是否为Android设备
   */
  isAndroid() {
    return this.systemInfo && this.systemInfo.platform === 'android'
  }

  /**
   * 是否为iOS设备
   */
  isIOS() {
    return this.systemInfo && this.systemInfo.platform === 'ios'
  }

  /**
   * 是否为平板设备
   */
  isTablet() {
    if (!this.systemInfo) return false
    
    const { windowWidth, windowHeight } = this.systemInfo
    const ratio = Math.max(windowWidth, windowHeight) / Math.min(windowWidth, windowHeight)
    
    // 平板设备通常宽高比较小
    return ratio < 1.6 && Math.min(windowWidth, windowHeight) > 600
  }

  /**
   * 是否为横屏
   */
  isLandscape() {
    if (!this.systemInfo) return false
    return this.systemInfo.windowWidth > this.systemInfo.windowHeight
  }

  /**
   * 获取屏幕密度
   */
  getPixelRatio() {
    return this.systemInfo ? this.systemInfo.pixelRatio : 1
  }

  /**
   * rpx转px
   */
  rpxToPx(rpx) {
    if (!this.systemInfo) return rpx
    return rpx * this.systemInfo.windowWidth / 750
  }

  /**
   * px转rpx
   */
  pxToRpx(px) {
    if (!this.systemInfo) return px
    return px * 750 / this.systemInfo.windowWidth
  }
}

/**
 * 屏幕适配管理类
 */
class ScreenAdapter {
  /**
   * 获取适配后的尺寸
   */
  static getAdaptiveSize(baseSize, minSize = 0, maxSize = Infinity) {
    const deviceManager = new DeviceManager()
    const systemInfo = deviceManager.getSystemInfo()
    
    if (!systemInfo) return baseSize

    const { windowWidth } = systemInfo
    const baseWidth = 375 // iPhone 6/7/8 基准宽度
    const scale = windowWidth / baseWidth

    let adaptiveSize = baseSize * scale

    // 限制最小和最大尺寸
    adaptiveSize = Math.max(minSize, Math.min(maxSize, adaptiveSize))

    return Math.round(adaptiveSize)
  }

  /**
   * 获取适配后的字体大小
   */
  static getAdaptiveFontSize(baseFontSize) {
    return this.getAdaptiveSize(baseFontSize, 12, 24)
  }

  /**
   * 获取适配后的间距
   */
  static getAdaptiveSpacing(baseSpacing) {
    return this.getAdaptiveSize(baseSpacing, 4, 40)
  }

  /**
   * 获取适配后的图标大小
   */
  static getAdaptiveIconSize(baseIconSize) {
    return this.getAdaptiveSize(baseIconSize, 16, 48)
  }
}

/**
 * 兼容性管理类
 */
class CompatibilityManager {
  /**
   * 检查API兼容性
   */
  static checkAPICompatibility(apiName) {
    return wx.canIUse(apiName)
  }

  /**
   * 安全调用API
   */
  static safeCallAPI(apiName, options = {}) {
    if (this.checkAPICompatibility(apiName)) {
      return wx[apiName](options)
    } else {
      console.warn(`API ${apiName} 不支持`)
      if (options.fail) {
        options.fail({ errMsg: `${apiName} is not supported` })
      }
      return Promise.reject(new Error(`${apiName} is not supported`))
    }
  }

  /**
   * 获取微信版本信息
   */
  static getWechatVersion() {
    const systemInfo = wx.getSystemInfoSync()
    return systemInfo.version
  }

  /**
   * 检查微信版本
   */
  static checkWechatVersion(targetVersion) {
    const currentVersion = this.getWechatVersion()
    return this.compareVersion(currentVersion, targetVersion) >= 0
  }

  /**
   * 版本号比较
   */
  static compareVersion(version1, version2) {
    const v1 = version1.split('.')
    const v2 = version2.split('.')
    const len = Math.max(v1.length, v2.length)

    while (v1.length < len) {
      v1.push('0')
    }
    while (v2.length < len) {
      v2.push('0')
    }

    for (let i = 0; i < len; i++) {
      const num1 = parseInt(v1[i])
      const num2 = parseInt(v2[i])

      if (num1 > num2) {
        return 1
      } else if (num1 < num2) {
        return -1
      }
    }

    return 0
  }

  /**
   * 获取支持的功能列表
   */
  static getSupportedFeatures() {
    const features = {
      // 基础功能
      chooseImage: this.checkAPICompatibility('chooseImage'),
      chooseVideo: this.checkAPICompatibility('chooseVideo'),
      chooseFile: this.checkAPICompatibility('chooseMessageFile'),
      
      // 分享功能
      shareAppMessage: this.checkAPICompatibility('onShareAppMessage'),
      shareTimeline: this.checkAPICompatibility('onShareTimeline'),
      
      // 支付功能
      requestPayment: this.checkAPICompatibility('requestPayment'),
      
      // 广告功能
      rewardedVideoAd: this.checkAPICompatibility('createRewardedVideoAd'),
      interstitialAd: this.checkAPICompatibility('createInterstitialAd'),
      
      // 其他功能
      vibrate: this.checkAPICompatibility('vibrateShort'),
      clipboard: this.checkAPICompatibility('setClipboardData'),
      camera: this.checkAPICompatibility('createCameraContext')
    }

    return features
  }
}

/**
 * 性能优化管理类
 */
class PerformanceManager {
  /**
   * 图片懒加载
   */
  static createLazyLoader(selector = '.lazy-image') {
    const observer = wx.createIntersectionObserver()
    
    observer.relativeToViewport({ bottom: 100 })
    observer.observe(selector, (res) => {
      if (res.intersectionRatio > 0) {
        // 图片进入可视区域
        const dataset = res.target.dataset
        if (dataset.src && !dataset.loaded) {
          // 加载图片
          res.target.src = dataset.src
          res.target.dataset.loaded = true
        }
      }
    })

    return observer
  }

  /**
   * 防抖函数
   */
  static debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  /**
   * 节流函数
   */
  static throttle(func, limit) {
    let inThrottle
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  /**
   * 内存使用监控
   */
  static monitorMemory() {
    if (wx.onMemoryWarning) {
      wx.onMemoryWarning(() => {
        console.warn('内存不足警告')
        // 清理缓存
        this.clearCache()
      })
    }
  }

  /**
   * 清理缓存
   */
  static clearCache() {
    try {
      // 清理图片缓存
      wx.clearStorage()
      console.log('缓存清理完成')
    } catch (error) {
      console.error('清理缓存失败:', error)
    }
  }
}

module.exports = {
  DeviceManager,
  ScreenAdapter,
  CompatibilityManager,
  PerformanceManager
}
