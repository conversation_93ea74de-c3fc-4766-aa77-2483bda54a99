// pages/admin/admin.js
const { showError, showSuccess, showLoading, hideLoading, showConfirm } = require('../../utils/util.js')

Page({
  data: {
    // 管理员信息
    isAdmin: false,
    adminInfo: null,
    
    // 统计数据
    stats: {
      totalUsers: 0,
      totalMaterials: 0,
      totalDownloads: 0,
      totalPoints: 0,
      pendingMaterials: 0,
      activeUsers: 0
    },
    
    // 待审核资料
    pendingMaterials: [],
    
    // 用户列表
    users: [],
    
    // 当前选项卡
    currentTab: 'stats',
    
    // 加载状态
    loading: true,
    refreshing: false
  },

  onLoad(options) {
    this.checkAdminPermission()
  },

  onShow() {
    if (this.data.isAdmin) {
      this.loadData()
    }
  },

  onPullDownRefresh() {
    this.loadData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 检查管理员权限
  async checkAdminPermission() {
    try {
      const app = getApp()
      if (!app.globalData.isLogin) {
        wx.showModal({
          title: '提示',
          content: '请先登录',
          showCancel: false,
          success: () => {
            wx.switchTab({
              url: '/pages/profile/profile'
            })
          }
        })
        return
      }

      // 调用云函数检查管理员权限
      const result = await wx.cloud.callFunction({
        name: 'checkAdminPermission',
        data: {}
      })

      if (result.result.success && result.result.data.isAdmin) {
        this.setData({
          isAdmin: true,
          adminInfo: result.result.data
        })
        this.loadData()
      } else {
        wx.showModal({
          title: '权限不足',
          content: '您没有管理员权限',
          showCancel: false,
          success: () => {
            wx.navigateBack()
          }
        })
      }
    } catch (error) {
      console.error('检查管理员权限失败:', error)
      showError('权限验证失败')
    }
  },

  // 加载数据
  async loadData() {
    if (!this.data.isAdmin) return

    try {
      this.setData({ loading: true })

      // 并行加载数据
      const [statsResult, materialsResult, usersResult] = await Promise.all([
        this.loadStats(),
        this.loadPendingMaterials(),
        this.loadUsers()
      ])

    } catch (error) {
      console.error('加载数据失败:', error)
      showError('数据加载失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getAdminStats',
        data: {}
      })

      if (result.result.success) {
        this.setData({
          stats: result.result.data
        })
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  // 加载待审核资料
  async loadPendingMaterials() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getPendingMaterials',
        data: {}
      })

      if (result.result.success) {
        this.setData({
          pendingMaterials: result.result.data || []
        })
      }
    } catch (error) {
      console.error('加载待审核资料失败:', error)
    }
  },

  // 加载用户列表
  async loadUsers() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getUsers',
        data: {
          limit: 50,
          orderBy: 'createTime',
          orderDirection: 'desc'
        }
      })

      if (result.result.success) {
        this.setData({
          users: result.result.data || []
        })
      }
    } catch (error) {
      console.error('加载用户列表失败:', error)
    }
  },

  // 切换选项卡
  onTabChange(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({
      currentTab: tab
    })
  },

  // 审核资料
  async onApproveMaterial(e) {
    const { material, action } = e.currentTarget.dataset
    
    const actionText = action === 'approve' ? '通过' : '拒绝'
    const confirm = await showConfirm(`确认${actionText}这个资料？`, '审核确认')
    if (!confirm) return

    try {
      showLoading(`${actionText}中...`)

      const result = await wx.cloud.callFunction({
        name: 'approveMaterial',
        data: {
          materialId: material._id,
          action: action,
          reason: action === 'reject' ? '不符合要求' : ''
        }
      })

      if (result.result.success) {
        showSuccess(`${actionText}成功`)
        // 重新加载待审核资料
        await this.loadPendingMaterials()
        await this.loadStats()
      } else {
        showError(result.result.message)
      }
    } catch (error) {
      console.error('审核资料失败:', error)
      showError('审核失败')
    } finally {
      hideLoading()
    }
  },

  // 查看资料详情
  onViewMaterial(e) {
    const { material } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${material._id}&admin=true`
    })
  },

  // 管理用户
  async onManageUser(e) {
    const { user, action } = e.currentTarget.dataset
    
    let actionText = ''
    let confirmText = ''
    
    switch (action) {
      case 'ban':
        actionText = '封禁'
        confirmText = '确认封禁此用户？'
        break
      case 'unban':
        actionText = '解封'
        confirmText = '确认解封此用户？'
        break
      case 'addPoints':
        actionText = '加积分'
        confirmText = '确认给此用户加积分？'
        break
      default:
        return
    }

    const confirm = await showConfirm(confirmText, '用户管理')
    if (!confirm) return

    try {
      showLoading(`${actionText}中...`)

      let data = {
        userId: user._id,
        action: action
      }

      if (action === 'addPoints') {
        // 简单示例，加100积分
        data.points = 100
      }

      const result = await wx.cloud.callFunction({
        name: 'manageUser',
        data: data
      })

      if (result.result.success) {
        showSuccess(`${actionText}成功`)
        // 重新加载用户列表
        await this.loadUsers()
        await this.loadStats()
      } else {
        showError(result.result.message)
      }
    } catch (error) {
      console.error('管理用户失败:', error)
      showError('操作失败')
    } finally {
      hideLoading()
    }
  },

  // 刷新数据
  async onRefresh() {
    this.setData({ refreshing: true })
    await this.loadData()
    this.setData({ refreshing: false })
    showSuccess('刷新完成')
  },

  // 导出数据
  async onExportData() {
    const confirm = await showConfirm('确认导出数据？', '数据导出')
    if (!confirm) return

    try {
      showLoading('导出中...')

      const result = await wx.cloud.callFunction({
        name: 'exportData',
        data: {
          type: 'all'
        }
      })

      if (result.result.success) {
        showSuccess('导出成功')
        // 这里可以处理导出的数据
        console.log('导出数据:', result.result.data)
      } else {
        showError(result.result.message)
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      showError('导出失败')
    } finally {
      hideLoading()
    }
  },

  // 格式化时间
  formatTime(timeStr) {
    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  }
})
