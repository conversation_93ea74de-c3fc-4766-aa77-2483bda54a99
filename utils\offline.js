/**
 * 离线功能工具类
 * 提供数据缓存、离线存储、数据同步等功能
 */

// 缓存键名常量
const CACHE_KEYS = {
  USER_INFO: 'cache_user_info',
  CATEGORIES: 'cache_categories',
  RECOMMEND_MATERIALS: 'cache_recommend_materials',
  SEARCH_HISTORY: 'cache_search_history',
  MATERIAL_DETAIL: 'cache_material_detail_',
  MATERIAL_LIST: 'cache_material_list_',
  MY_DOWNLOADS: 'cache_my_downloads',
  MY_FAVORITES: 'cache_my_favorites',
  POINTS_LOG: 'cache_points_log',
  OFFLINE_ACTIONS: 'offline_actions',
  LAST_SYNC_TIME: 'last_sync_time'
}

// 缓存过期时间（毫秒）
const CACHE_EXPIRE_TIME = {
  USER_INFO: 30 * 60 * 1000, // 30分钟
  CATEGORIES: 24 * 60 * 60 * 1000, // 24小时
  RECOMMEND_MATERIALS: 60 * 60 * 1000, // 1小时
  MATERIAL_DETAIL: 30 * 60 * 1000, // 30分钟
  MATERIAL_LIST: 10 * 60 * 1000, // 10分钟
  MY_DOWNLOADS: 5 * 60 * 1000, // 5分钟
  MY_FAVORITES: 5 * 60 * 1000, // 5分钟
  POINTS_LOG: 5 * 60 * 1000 // 5分钟
}

/**
 * 缓存管理类
 */
class CacheManager {
  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {number} expireTime 过期时间（毫秒）
   */
  static set(key, data, expireTime = 0) {
    try {
      const cacheData = {
        data,
        timestamp: Date.now(),
        expireTime
      }
      
      wx.setStorageSync(key, cacheData)
      return true
    } catch (error) {
      console.error('设置缓存失败:', error)
      return false
    }
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @returns {any|null} 缓存数据或null
   */
  static get(key) {
    try {
      const cacheData = wx.getStorageSync(key)
      if (!cacheData) return null

      // 检查是否过期
      if (cacheData.expireTime > 0) {
        const now = Date.now()
        if (now - cacheData.timestamp > cacheData.expireTime) {
          this.remove(key)
          return null
        }
      }

      return cacheData.data
    } catch (error) {
      console.error('获取缓存失败:', error)
      return null
    }
  }

  /**
   * 移除缓存
   * @param {string} key 缓存键
   */
  static remove(key) {
    try {
      wx.removeStorageSync(key)
    } catch (error) {
      console.error('移除缓存失败:', error)
    }
  }

  /**
   * 清空所有缓存
   */
  static clear() {
    try {
      Object.values(CACHE_KEYS).forEach(key => {
        this.remove(key)
      })
    } catch (error) {
      console.error('清空缓存失败:', error)
    }
  }

  /**
   * 获取缓存大小信息
   */
  static getStorageInfo() {
    try {
      return wx.getStorageInfoSync()
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return null
    }
  }
}

/**
 * 离线操作管理类
 */
class OfflineManager {
  /**
   * 添加离线操作
   * @param {string} action 操作类型
   * @param {object} data 操作数据
   */
  static addOfflineAction(action, data) {
    try {
      const actions = this.getOfflineActions()
      actions.push({
        id: Date.now() + Math.random(),
        action,
        data,
        timestamp: Date.now(),
        retryCount: 0
      })
      
      wx.setStorageSync(CACHE_KEYS.OFFLINE_ACTIONS, actions)
      return true
    } catch (error) {
      console.error('添加离线操作失败:', error)
      return false
    }
  }

  /**
   * 获取离线操作列表
   */
  static getOfflineActions() {
    try {
      return wx.getStorageSync(CACHE_KEYS.OFFLINE_ACTIONS) || []
    } catch (error) {
      console.error('获取离线操作失败:', error)
      return []
    }
  }

  /**
   * 移除离线操作
   * @param {string} actionId 操作ID
   */
  static removeOfflineAction(actionId) {
    try {
      const actions = this.getOfflineActions()
      const filteredActions = actions.filter(action => action.id !== actionId)
      wx.setStorageSync(CACHE_KEYS.OFFLINE_ACTIONS, filteredActions)
    } catch (error) {
      console.error('移除离线操作失败:', error)
    }
  }

  /**
   * 清空离线操作
   */
  static clearOfflineActions() {
    try {
      wx.removeStorageSync(CACHE_KEYS.OFFLINE_ACTIONS)
    } catch (error) {
      console.error('清空离线操作失败:', error)
    }
  }

  /**
   * 同步离线操作
   */
  static async syncOfflineActions() {
    const actions = this.getOfflineActions()
    if (actions.length === 0) return { success: true, synced: 0 }

    let syncedCount = 0
    const failedActions = []

    for (const action of actions) {
      try {
        const result = await this.executeOfflineAction(action)
        if (result.success) {
          syncedCount++
        } else {
          action.retryCount++
          if (action.retryCount < 3) {
            failedActions.push(action)
          }
        }
      } catch (error) {
        console.error('执行离线操作失败:', error)
        action.retryCount++
        if (action.retryCount < 3) {
          failedActions.push(action)
        }
      }
    }

    // 更新失败的操作
    wx.setStorageSync(CACHE_KEYS.OFFLINE_ACTIONS, failedActions)

    return {
      success: true,
      synced: syncedCount,
      failed: failedActions.length
    }
  }

  /**
   * 执行离线操作
   * @param {object} action 操作对象
   */
  static async executeOfflineAction(action) {
    const { action: actionType, data } = action

    switch (actionType) {
      case 'favorite':
        return await this.syncFavoriteAction(data)
      case 'download':
        return await this.syncDownloadAction(data)
      case 'points':
        return await this.syncPointsAction(data)
      default:
        return { success: false, message: '未知操作类型' }
    }
  }

  /**
   * 同步收藏操作
   */
  static async syncFavoriteAction(data) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'manageFavorite',
        data
      })
      return result.result
    } catch (error) {
      console.error('同步收藏操作失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 同步下载操作
   */
  static async syncDownloadAction(data) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'downloadMaterial',
        data
      })
      return result.result
    } catch (error) {
      console.error('同步下载操作失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 同步积分操作
   */
  static async syncPointsAction(data) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'earnPointsByShare',
        data
      })
      return result.result
    } catch (error) {
      console.error('同步积分操作失败:', error)
      return { success: false, message: error.message }
    }
  }
}

/**
 * 数据同步管理类
 */
class SyncManager {
  /**
   * 检查是否需要同步
   */
  static needSync() {
    try {
      const lastSyncTime = wx.getStorageSync(CACHE_KEYS.LAST_SYNC_TIME) || 0
      const now = Date.now()
      const syncInterval = 5 * 60 * 1000 // 5分钟

      return now - lastSyncTime > syncInterval
    } catch (error) {
      console.error('检查同步状态失败:', error)
      return true
    }
  }

  /**
   * 执行数据同步
   */
  static async performSync() {
    try {
      // 同步离线操作
      const offlineResult = await OfflineManager.syncOfflineActions()
      
      // 更新最后同步时间
      wx.setStorageSync(CACHE_KEYS.LAST_SYNC_TIME, Date.now())

      return {
        success: true,
        offlineActions: offlineResult
      }
    } catch (error) {
      console.error('数据同步失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 自动同步（在网络可用时）
   */
  static async autoSync() {
    // 检查网络状态
    const networkType = await this.getNetworkType()
    if (networkType === 'none') {
      return { success: false, message: '网络不可用' }
    }

    // 检查是否需要同步
    if (!this.needSync()) {
      return { success: true, message: '无需同步' }
    }

    return await this.performSync()
  }

  /**
   * 获取网络类型
   */
  static getNetworkType() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => resolve(res.networkType),
        fail: () => resolve('unknown')
      })
    })
  }
}

module.exports = {
  CACHE_KEYS,
  CACHE_EXPIRE_TIME,
  CacheManager,
  OfflineManager,
  SyncManager
}
