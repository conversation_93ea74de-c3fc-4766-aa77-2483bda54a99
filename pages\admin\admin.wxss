/* pages/admin/admin.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 权限不足 */
.no-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
}

/* 管理后台内容 */
.admin-content {
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* 顶部导航 */
.tab-nav {
  display: flex;
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  padding: 30rpx 20rpx;
  text-align: center;
  font-size: 32rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #FF6B35;
  border-bottom-color: #FF6B35;
  font-weight: 600;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  color: #999;
}

/* 统计页面 */
.stats-content {
  padding: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.stat-card {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF6B35;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  background-color: #FF6B35;
  color: white;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  border: none;
}

.action-btn.secondary {
  background-color: #f0f0f0;
  color: #666;
}

.action-btn[disabled] {
  background-color: #ccc;
}

/* 资料审核页面 */
.materials-content {
  padding: 20rpx;
}

.materials-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.material-item {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.material-info {
  margin-bottom: 30rpx;
}

.material-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.material-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.material-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.meta-item {
  font-size: 24rpx;
  color: #999;
  background-color: #f8f8f8;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.material-actions {
  display: flex;
  gap: 20rpx;
}

.approve-btn,
.reject-btn,
.view-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.approve-btn {
  background-color: #52c41a;
  color: white;
}

.reject-btn {
  background-color: #ff4d4f;
  color: white;
}

.view-btn {
  background-color: #f0f0f0;
  color: #666;
}

/* 用户管理页面 */
.users-content {
  padding: 20rpx;
}

.users-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.user-item {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.user-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.ban-btn,
.unban-btn,
.points-btn {
  padding: 15rpx 25rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  min-width: 120rpx;
}

.ban-btn {
  background-color: #ff4d4f;
  color: white;
}

.unban-btn {
  background-color: #52c41a;
  color: white;
}

.points-btn {
  background-color: #1890ff;
  color: white;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}
