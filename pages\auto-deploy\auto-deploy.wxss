/* pages/auto-deploy/auto-deploy.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  margin-bottom: 20rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
}

/* 环境信息 */
.env-info {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.env-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.env-details {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.env-item {
  display: flex;
  align-items: center;
}

.env-label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.env-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 部署按钮 */
.deploy-section {
  margin-bottom: 20rpx;
}

.deploy-btn {
  width: 100%;
  background-color: #FF6B35;
  color: white;
  border-radius: 50rpx;
  padding: 35rpx;
  font-size: 36rpx;
  font-weight: 600;
  border: none;
  margin-bottom: 20rpx;
}

.deploy-btn[disabled] {
  background-color: #ccc;
}

.redeploy-section {
  text-align: center;
}

.redeploy-btn {
  background-color: #1890ff;
  color: white;
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 28rpx;
  border: none;
}

/* 部署成功提示 */
.deploy-success {
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.deploy-success text {
  font-size: 32rpx;
  color: #52c41a;
  font-weight: 600;
}

/* 部署步骤 */
.deploy-steps {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.steps-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-item {
  background-color: #f8f8f8;
  border-radius: 15rpx;
  padding: 25rpx;
  border-left: 4rpx solid #ddd;
  transition: all 0.3s ease;
}

.step-item.active {
  border-left-color: #FF6B35;
}

.step-item.success {
  border-left-color: #52c41a;
  background-color: #f6ffed;
}

.step-item.failed {
  border-left-color: #ff4d4f;
  background-color: #fff2f0;
}

.step-item.running {
  border-left-color: #1890ff;
  background-color: #e6f7ff;
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.step-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.step-content {
  flex: 1;
}

.step-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
  font-weight: 600;
}

.step-item.active .step-number {
  background-color: #FF6B35;
}

.step-item.success .step-number {
  background-color: #52c41a;
}

.step-item.failed .step-number {
  background-color: #ff4d4f;
}

.step-progress {
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 10rpx;
  padding: 15rpx;
  margin-top: 15rpx;
}

.step-progress text {
  font-size: 24rpx;
  color: #1890ff;
}

.step-result {
  background-color: rgba(82, 196, 26, 0.1);
  border-radius: 10rpx;
  padding: 15rpx;
  margin-top: 15rpx;
}

.result-message {
  font-size: 24rpx;
  color: #52c41a;
}

.step-error {
  background-color: rgba(255, 77, 79, 0.1);
  border-radius: 10rpx;
  padding: 15rpx;
  margin-top: 15rpx;
}

.error-message {
  font-size: 24rpx;
  color: #ff4d4f;
}

/* 部署结果 */
.deploy-results {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.results-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.result-item {
  background-color: #f8f8f8;
  border-radius: 15rpx;
  padding: 20rpx;
  border-left: 4rpx solid #ddd;
}

.result-item.success {
  border-left-color: #52c41a;
  background-color: #f6ffed;
}

.result-item.failed {
  border-left-color: #ff4d4f;
  background-color: #fff2f0;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
}

.result-step {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.result-message {
  margin-bottom: 10rpx;
}

.result-message text {
  font-size: 24rpx;
  color: #666;
}

.result-actions {
  text-align: right;
}

.details-btn {
  background-color: #1890ff;
  color: white;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 22rpx;
  border: none;
}

/* 说明信息 */
.deploy-info,
.manual-deploy {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-title,
.manual-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.info-content,
.manual-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-content text,
.manual-content text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 动画效果 */
.step-item.running .step-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
