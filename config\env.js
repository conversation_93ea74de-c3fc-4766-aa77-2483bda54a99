/**
 * 环境配置文件
 * 管理不同环境的配置信息
 */

// 环境配置
const environments = {
  // 开发环境
  development: {
    cloudEnvId: 'cloud1-8gm001v7fd56ff43',
    debug: true,
    useMockData: false, // 现在有真实云环境，不使用模拟数据
    apiTimeout: 10000,
    logLevel: 'debug'
  },
  
  // 测试环境
  testing: {
    cloudEnvId: 'cloud1-8gm001v7fd56ff43', // 可以使用同一个环境或单独的测试环境
    debug: true,
    useMockData: false,
    apiTimeout: 8000,
    logLevel: 'info'
  },
  
  // 生产环境
  production: {
    cloudEnvId: 'cloud1-8gm001v7fd56ff43', // 生产环境应该使用独立的环境ID
    debug: false,
    useMockData: false,
    apiTimeout: 5000,
    logLevel: 'error'
  }
}

/**
 * 获取当前环境
 */
function getCurrentEnv() {
  try {
    const accountInfo = wx.getAccountInfoSync()
    const envVersion = accountInfo.miniProgram.envVersion
    
    switch (envVersion) {
      case 'develop':
        return 'development'
      case 'trial':
        return 'testing'
      case 'release':
        return 'production'
      default:
        return 'development'
    }
  } catch (error) {
    console.error('获取环境信息失败:', error)
    return 'development'
  }
}

/**
 * 获取当前环境配置
 */
function getConfig() {
  const currentEnv = getCurrentEnv()
  const config = environments[currentEnv]
  
  console.log(`当前环境: ${currentEnv}`, config)
  
  return {
    ...config,
    currentEnv
  }
}

/**
 * 是否为开发环境
 */
function isDevelopment() {
  return getCurrentEnv() === 'development'
}

/**
 * 是否为生产环境
 */
function isProduction() {
  return getCurrentEnv() === 'production'
}

/**
 * 是否启用调试模式
 */
function isDebugMode() {
  const config = getConfig()
  return config.debug
}

module.exports = {
  environments,
  getCurrentEnv,
  getConfig,
  isDevelopment,
  isProduction,
  isDebugMode
}
