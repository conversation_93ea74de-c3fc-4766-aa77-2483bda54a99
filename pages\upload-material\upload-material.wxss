/* pages/upload-material/upload-material.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  color: #999;
}

.form-container {
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* 表单区块 */
.form-section {
  background-color: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单项 */
.form-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  font-size: 30rpx;
  border: none;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  font-size: 30rpx;
  border: none;
}

.form-picker {
  width: 100%;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
}

.picker-text {
  font-size: 30rpx;
  color: #333;
}

.form-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 文件上传区域 */
.file-upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  border: 2rpx dashed #ddd;
  border-radius: 20rpx;
  margin: 30rpx;
  background-color: #fafafa;
}

.upload-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 文件信息 */
.file-info {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 20rpx;
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.file-name {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.remove-btn {
  background-color: #ff4d4f;
  color: white;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  border: none;
}

.file-details {
  display: flex;
  gap: 40rpx;
  margin-bottom: 20rpx;
}

.file-size,
.file-type {
  font-size: 28rpx;
  color: #666;
}

/* 文件建议 */
.file-recommendations {
  margin-top: 20rpx;
}

.recommendation {
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

.recommendation.warning {
  background-color: #fff7e6;
  color: #fa8c16;
  border-left: 4rpx solid #fa8c16;
}

.recommendation.info {
  background-color: #e6f7ff;
  color: #1890ff;
  border-left: 4rpx solid #1890ff;
}

.recommendation.tip {
  background-color: #f6ffed;
  color: #52c41a;
  border-left: 4rpx solid #52c41a;
}

/* 提交区域 */
.submit-section {
  padding: 40rpx 20rpx;
}

.submit-btn {
  width: 100%;
  background-color: #FF6B35;
  color: white;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  border: none;
}

.submit-btn[disabled] {
  background-color: #ccc;
}

.submit-tip {
  text-align: center;
  margin-top: 20rpx;
}

.submit-tip text {
  font-size: 24rpx;
  color: #999;
}
