/**
 * 部署配置文件
 * 包含不同环境的配置信息
 */

// 环境配置
const environments = {
  // 开发环境
  development: {
    name: '开发环境',
    cloudEnv: 'wx-k12-education-dev',
    appId: 'wx-dev-app-id',
    version: '1.0.0-dev',
    description: '开发测试版本',
    features: {
      debug: true,
      mockData: true,
      testMode: true,
      analytics: false
    },
    apis: {
      baseUrl: 'https://dev-api.example.com',
      timeout: 10000
    },
    storage: {
      maxSize: '10MB',
      cacheExpire: 5 * 60 * 1000 // 5分钟
    }
  },

  // 测试环境
  testing: {
    name: '测试环境',
    cloudEnv: 'wx-k12-education-test',
    appId: 'wx-test-app-id',
    version: '1.0.0-beta',
    description: '测试版本',
    features: {
      debug: true,
      mockData: false,
      testMode: true,
      analytics: true
    },
    apis: {
      baseUrl: 'https://test-api.example.com',
      timeout: 8000
    },
    storage: {
      maxSize: '20MB',
      cacheExpire: 10 * 60 * 1000 // 10分钟
    }
  },

  // 生产环境
  production: {
    name: '生产环境',
    cloudEnv: 'wx-k12-education-prod',
    appId: 'wx-prod-app-id',
    version: '1.0.0',
    description: '正式版本',
    features: {
      debug: false,
      mockData: false,
      testMode: false,
      analytics: true
    },
    apis: {
      baseUrl: 'https://api.example.com',
      timeout: 5000
    },
    storage: {
      maxSize: '50MB',
      cacheExpire: 30 * 60 * 1000 // 30分钟
    }
  }
}

// 部署检查清单
const deploymentChecklist = {
  // 代码检查
  codeReview: [
    '代码已通过Code Review',
    '没有console.log等调试代码',
    '没有TODO或FIXME注释',
    '代码格式化完成',
    '变量命名规范'
  ],

  // 功能测试
  functionalTests: [
    '单元测试通过',
    '集成测试通过',
    '用户流程测试完成',
    '边界条件测试完成',
    '错误处理测试完成'
  ],

  // 性能测试
  performanceTests: [
    '页面加载时间 < 3秒',
    '接口响应时间 < 1秒',
    '内存使用正常',
    '图片加载优化',
    '长列表性能优化'
  ],

  // 兼容性测试
  compatibilityTests: [
    'iOS设备测试完成',
    'Android设备测试完成',
    '不同屏幕尺寸适配',
    '微信版本兼容性测试',
    '网络环境测试'
  ],

  // 安全检查
  securityChecks: [
    '用户数据加密',
    'API接口鉴权',
    '敏感信息保护',
    '防止XSS攻击',
    '数据传输安全'
  ],

  // 配置检查
  configurationChecks: [
    '环境变量配置正确',
    '云函数部署完成',
    '数据库配置正确',
    '存储权限设置',
    '域名配置完成'
  ]
}

// 部署步骤
const deploymentSteps = {
  // 预部署步骤
  preDeployment: [
    {
      step: 1,
      name: '代码检查',
      description: '运行代码检查工具，确保代码质量',
      command: 'npm run lint',
      required: true
    },
    {
      step: 2,
      name: '运行测试',
      description: '执行所有测试用例',
      command: 'npm run test',
      required: true
    },
    {
      step: 3,
      name: '构建项目',
      description: '构建生产版本',
      command: 'npm run build',
      required: true
    },
    {
      step: 4,
      name: '备份数据',
      description: '备份当前生产数据',
      command: 'npm run backup',
      required: false
    }
  ],

  // 部署步骤
  deployment: [
    {
      step: 1,
      name: '上传云函数',
      description: '部署所有云函数到云端',
      command: 'npm run deploy:functions',
      required: true
    },
    {
      step: 2,
      name: '更新数据库',
      description: '执行数据库迁移脚本',
      command: 'npm run migrate',
      required: false
    },
    {
      step: 3,
      name: '上传小程序',
      description: '上传小程序代码到微信平台',
      command: 'npm run upload',
      required: true
    },
    {
      step: 4,
      name: '提交审核',
      description: '提交小程序版本审核',
      manual: true,
      required: true
    }
  ],

  // 后部署步骤
  postDeployment: [
    {
      step: 1,
      name: '健康检查',
      description: '检查服务是否正常运行',
      command: 'npm run health-check',
      required: true
    },
    {
      step: 2,
      name: '监控设置',
      description: '启用监控和告警',
      manual: true,
      required: true
    },
    {
      step: 3,
      name: '文档更新',
      description: '更新部署文档和版本记录',
      manual: true,
      required: false
    }
  ]
}

// 回滚计划
const rollbackPlan = {
  triggers: [
    '严重bug导致功能不可用',
    '性能严重下降',
    '用户投诉激增',
    '数据丢失或损坏',
    '安全漏洞发现'
  ],
  
  steps: [
    {
      step: 1,
      name: '停止新版本',
      description: '立即停止新版本的服务',
      timeLimit: '5分钟'
    },
    {
      step: 2,
      name: '恢复代码',
      description: '回滚到上一个稳定版本',
      timeLimit: '10分钟'
    },
    {
      step: 3,
      name: '恢复数据',
      description: '如需要，恢复数据库备份',
      timeLimit: '30分钟'
    },
    {
      step: 4,
      name: '验证功能',
      description: '验证回滚后功能正常',
      timeLimit: '15分钟'
    },
    {
      step: 5,
      name: '通知用户',
      description: '通知用户问题已解决',
      timeLimit: '5分钟'
    }
  ]
}

// 监控指标
const monitoringMetrics = {
  // 性能指标
  performance: [
    {
      name: '页面加载时间',
      threshold: '3秒',
      alert: '超过5秒'
    },
    {
      name: 'API响应时间',
      threshold: '1秒',
      alert: '超过3秒'
    },
    {
      name: '内存使用率',
      threshold: '80%',
      alert: '超过90%'
    }
  ],

  // 业务指标
  business: [
    {
      name: '用户活跃度',
      threshold: 'DAU > 1000',
      alert: 'DAU下降20%'
    },
    {
      name: '下载成功率',
      threshold: '> 95%',
      alert: '< 90%'
    },
    {
      name: '用户留存率',
      threshold: '> 60%',
      alert: '< 50%'
    }
  ],

  // 错误指标
  errors: [
    {
      name: '错误率',
      threshold: '< 1%',
      alert: '> 5%'
    },
    {
      name: '崩溃率',
      threshold: '< 0.1%',
      alert: '> 1%'
    }
  ]
}

module.exports = {
  environments,
  deploymentChecklist,
  deploymentSteps,
  rollbackPlan,
  monitoringMetrics
}
