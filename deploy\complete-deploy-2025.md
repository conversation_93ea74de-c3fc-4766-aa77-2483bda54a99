# 🚀 2025年完整云函数部署方案

## 📋 当前状态确认

**云环境ID**: `cloud1-8gm001v7fd56ff43` ✅  
**项目代码**: 已完成 ✅  
**需要部署**: 21个云函数 + 数据库初始化

## 🔧 2025年最新部署方法

### 方法一：单个云函数部署 ⭐ 最可靠

根据2025年最新资料，推荐使用单个云函数逐一部署的方式：

#### 步骤详解：

1. **确认项目结构**
   ```
   项目根目录/
   ├── cloudfunctions/
   │   ├── initDatabase/
   │   ├── getCategories/
   │   ├── getRecommendMaterials/
   │   └── ... (其他云函数)
   ```

2. **逐个部署云函数**
   ```
   对于每个云函数文件夹：
   1. 右键点击具体的云函数文件夹（如 cloudfunctions/initDatabase）
   2. 在右键菜单中查找以下选项之一：
      - "上传并部署：云端安装依赖"
      - "创建并部署：云端安装依赖" 
      - "上传并部署：云端安装依赖(不上传node_modules)"
   3. 点击选项，等待部署完成
   4. 重复此过程部署所有云函数
   ```

3. **部署优先级**
   ```
   第一批（核心功能）：
   - initDatabase（最重要）
   - getCategories
   - getRecommendMaterials
   - login
   - getUserInfo
   
   第二批（基础功能）：
   - searchMaterials
   - getMaterialDetail
   - downloadMaterial
   - manageFavorite
   - getMyDownloads
   - getMyFavorites
   
   第三批（高级功能）：
   - earnPointsByAd
   - earnPointsByShare
   - earnPointsByCheckin
   - getPointsLog
   - generateShareCode
   - handleShareInvite
   - uploadMaterial
   - submitFeedback
   - checkAdminPermission
   ```

### 方法二：云开发控制台部署 🌐 备用方案

如果右键菜单不可用，使用云开发控制台：

#### 步骤详解：

1. **打开云开发控制台**
   ```
   方式1：点击工具栏 "工具" → "云开发"
   方式2：点击工具栏的云开发图标 ☁️
   方式3：在项目中右键 cloudfunctions 文件夹 → "当前环境"
   ```

2. **进入云函数管理**
   ```
   1. 在控制台左侧菜单选择 "云函数"
   2. 点击 "函数列表"
   3. 确认环境为：cloud1-8gm001v7fd56ff43
   ```

3. **创建云函数**
   ```
   方式1：点击 "新建云函数"
   方式2：点击 "从本地上传"（如果有此选项）
   
   对于每个云函数：
   1. 输入函数名称（如：initDatabase）
   2. 选择运行环境：Node.js 16
   3. 复制对应的 index.js 代码
   4. 复制对应的 package.json 代码
   5. 点击 "保存并部署"
   ```

### 方法三：命令行部署 💻 高级方案

如果以上方法都不可用，使用微信云开发CLI：

#### 安装和配置：

```bash
# 1. 安装微信云开发CLI
npm install -g @cloudbase/cli

# 2. 登录
tcb login

# 3. 初始化项目
tcb init

# 4. 部署云函数
tcb functions:deploy initDatabase
tcb functions:deploy getCategories
# ... 逐个部署其他函数
```

## 📝 需要创建的云函数代码

### 核心云函数模板

我已经为您准备了 `initDatabase` 云函数，其他云函数需要创建：

#### 1. getCategories 云函数

```javascript
// cloudfunctions/getCategories/index.js
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const result = await db.collection('categories').get()
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    return {
      success: false,
      message: error.message
    }
  }
}
```

#### 2. getRecommendMaterials 云函数

```javascript
// cloudfunctions/getRecommendMaterials/index.js
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { page = 1, limit = 10 } = event
    const skip = (page - 1) * limit
    
    const result = await db.collection('materials')
      .where({ is_active: true })
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()
    
    return {
      success: true,
      data: {
        materials: result.data,
        hasMore: result.data.length === limit,
        total: result.data.length
      }
    }
  } catch (error) {
    return {
      success: false,
      message: error.message
    }
  }
}
```

#### 3. login 云函数

```javascript
// cloudfunctions/login/index.js
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { userInfo } = event
  
  try {
    // 检查用户是否已存在
    const userResult = await db.collection('users').doc(wxContext.OPENID).get()
    
    if (userResult.data) {
      // 更新用户信息
      await db.collection('users').doc(wxContext.OPENID).update({
        data: {
          ...userInfo,
          lastActiveTime: new Date()
        }
      })
    } else {
      // 创建新用户
      await db.collection('users').doc(wxContext.OPENID).set({
        data: {
          _openid: wxContext.OPENID,
          ...userInfo,
          points: 100, // 新用户奖励
          createTime: new Date(),
          lastActiveTime: new Date()
        }
      })
    }
    
    return {
      success: true,
      data: {
        openid: wxContext.OPENID,
        userInfo
      }
    }
  } catch (error) {
    return {
      success: false,
      message: error.message
    }
  }
}
```

## 🔍 部署验证步骤

### 1. 检查部署状态
```
在云开发控制台 → 云函数 → 函数列表中：
- 确认所有函数状态为 "部署成功" 或 "正常"
- 检查函数的最后更新时间
- 查看是否有错误日志
```

### 2. 测试云函数
```
方式1：在控制台点击函数名 → "测试" 按钮
方式2：运行小程序中的自动部署页面
方式3：使用部署检查工具
```

### 3. 验证数据库
```
1. 运行 initDatabase 云函数
2. 检查数据库集合是否创建成功
3. 验证初始数据是否导入正确
```

## 🆘 常见问题解决

### 问题1：右键菜单没有部署选项
```
解决方案：
1. 确认已开通云开发服务
2. 检查是否选择了正确的云环境
3. 尝试刷新开发者工具
4. 确认 cloudfunctions 文件夹在项目根目录
5. 使用云开发控制台手动创建
```

### 问题2：部署失败或超时
```
解决方案：
1. 检查网络连接
2. 确认云环境状态正常
3. 查看错误日志详情
4. 尝试重新部署
5. 检查代码语法是否正确
```

### 问题3：函数调用失败
```
解决方案：
1. 检查函数代码是否有语法错误
2. 确认依赖包是否正确安装
3. 查看云函数运行日志
4. 验证数据库权限设置
```

## 📞 获取最新帮助

### 官方资源
- [微信云开发文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/)
- [开发者社区](https://developers.weixin.qq.com/community/)
- [云开发控制台](https://console.cloud.tencent.com/tcb)

### 技术支持
- 微信开发者社区提问
- 查看官方更新日志
- 关注工具版本更新

---

## 🎯 推荐操作流程

1. **首先尝试方法一**（单个云函数右键部署）
2. **如果不行，使用方法二**（云开发控制台）
3. **最后考虑方法三**（命令行工具）

**部署完成后，运行小程序的自动部署页面完成数据库初始化！**
