# Simple Cloud Function Deploy Script - 2025
# Fixed version for tcb CLI

param(
    [switch]$DryRun = $false,
    [int]$Priority = 0
)

# Configuration
$envId = "cloud1-8gm001v7fd56ff43"

# Cloud Functions List (Priority Order)
$functions = @(
    @{ Name = "initDatabase"; Priority = 1; Desc = "Database Init" },
    @{ Name = "getCategories"; Priority = 1; Desc = "Get Categories" },
    @{ Name = "getRecommendMaterials"; Priority = 1; Desc = "Get Recommend" },
    @{ Name = "login"; Priority = 1; Desc = "User Login" },
    @{ Name = "searchMaterials"; Priority = 1; Desc = "Search Materials" },
    @{ Name = "getMaterialDetail"; Priority = 2; Desc = "Material Detail" },
    @{ Name = "downloadMaterial"; Priority = 2; Desc = "Download Material" },
    @{ Name = "manageFavorite"; Priority = 2; Desc = "Manage Favorite" },
    @{ Name = "getMyDownloads"; Priority = 2; Desc = "My Downloads" },
    @{ Name = "getMyFavorites"; Priority = 2; Desc = "My Favorites" },
    @{ Name = "generateShareCode"; Priority = 3; Desc = "Generate Share" },
    @{ Name = "handleShareInvite"; Priority = 3; Desc = "Handle Share" },
    @{ Name = "getShareStats"; Priority = 3; Desc = "Share Stats" },
    @{ Name = "recordShareClick"; Priority = 3; Desc = "Record Click" },
    @{ Name = "uploadMaterial"; Priority = 3; Desc = "Upload Material" },
    @{ Name = "validateFile"; Priority = 3; Desc = "Validate File" },
    @{ Name = "submitFeedback"; Priority = 3; Desc = "Submit Feedback" },
    @{ Name = "getAnnouncements"; Priority = 3; Desc = "Get Announcements" },
    @{ Name = "checkAdminPermission"; Priority = 3; Desc = "Check Admin" },
    @{ Name = "getAdminStats"; Priority = 3; Desc = "Admin Stats" }
)

Write-Host "Cloud Function Deploy Tool - 2025" -ForegroundColor Cyan
Write-Host "Environment: $envId" -ForegroundColor Yellow
Write-Host ""

# Check tcb CLI
try {
    $version = tcb --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "tcb CLI version: $version" -ForegroundColor Green
    } else {
        Write-Host "ERROR: tcb CLI not found. Please install: npm install -g @cloudbase/cli" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "ERROR: tcb CLI not available" -ForegroundColor Red
    exit 1
}

# Filter functions by priority
$deployFunctions = $functions
if ($Priority -gt 0) {
    $deployFunctions = $functions | Where-Object { $_.Priority -eq $Priority }
    Write-Host "Deploying Priority $Priority functions only" -ForegroundColor Magenta
}

Write-Host "Functions to deploy: $($deployFunctions.Count)" -ForegroundColor Cyan
Write-Host ""

# Dry run mode
if ($DryRun) {
    Write-Host "DRY RUN MODE - No actual deployment" -ForegroundColor Magenta
    Write-Host ""
    foreach ($func in $deployFunctions) {
        Write-Host "  - $($func.Name) ($($func.Desc))" -ForegroundColor Yellow
    }
    Write-Host ""
    Write-Host "To deploy, remove -DryRun parameter" -ForegroundColor Cyan
    exit 0
}

# Deploy functions
$success = 0
$failed = 0
$failedList = @()

foreach ($func in $deployFunctions) {
    $name = $func.Name
    $desc = $func.Desc
    
    Write-Host "Deploying: $name ($desc)" -ForegroundColor Yellow
    
    try {
        $result = tcb fn deploy $name -e $envId 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS: $name deployed" -ForegroundColor Green
            $success++
        } else {
            Write-Host "FAILED: $name - $result" -ForegroundColor Red
            $failed++
            $failedList += $name
        }
    } catch {
        Write-Host "ERROR: $name - $_" -ForegroundColor Red
        $failed++
        $failedList += $name
    }
    
    # Add delay to avoid rate limiting
    Start-Sleep -Seconds 2
}

# Summary
Write-Host ""
Write-Host "DEPLOYMENT SUMMARY:" -ForegroundColor Cyan
Write-Host "Total: $($deployFunctions.Count)" -ForegroundColor Blue
Write-Host "Success: $success" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red

if ($failed -gt 0) {
    Write-Host ""
    Write-Host "Failed functions:" -ForegroundColor Red
    foreach ($name in $failedList) {
        Write-Host "  - $name" -ForegroundColor Red
    }
}

if ($success -gt 0) {
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Test functions in WeChat DevTools" -ForegroundColor Yellow
    Write-Host "2. Run initDatabase to setup database" -ForegroundColor Yellow
    Write-Host "3. Test core features" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Deployment completed!" -ForegroundColor Green
