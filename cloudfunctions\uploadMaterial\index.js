// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { 
    title, 
    description, 
    grade, 
    subject, 
    semester, 
    textbook, 
    type, 
    points,
    fileId,
    fileName,
    fileSize,
    tags = []
  } = event
  
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    // 验证必填字段
    if (!title || !description || !grade || !subject || !fileId) {
      return {
        success: false,
        message: '缺少必填字段'
      }
    }

    // 验证文件大小（限制50MB）
    if (fileSize > 50 * 1024 * 1024) {
      return {
        success: false,
        message: '文件大小不能超过50MB'
      }
    }

    // 验证文件格式
    const allowedExtensions = ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx']
    const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
    
    if (!allowedExtensions.includes(fileExtension)) {
      return {
        success: false,
        message: '不支持的文件格式，仅支持PDF、Word、PPT、Excel文件'
      }
    }

    // 验证积分设置
    const config = await getConfig()
    const materialPoints = points || config.default_material_points || 100
    
    if (materialPoints < config.min_material_points || materialPoints > config.max_material_points) {
      return {
        success: false,
        message: `积分设置必须在${config.min_material_points}-${config.max_material_points}之间`
      }
    }

    // 生成文件存储路径
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)
    const storagePath = `materials/${grade}/${subject}/${timestamp}_${randomStr}${fileExtension}`

    // 创建资料记录
    const materialData = {
      title: title.trim(),
      description: description.trim(),
      grade,
      subject,
      semester: semester || '',
      textbook: textbook || '',
      type: type || 'document',
      points: materialPoints,
      file_id: fileId,
      file_name: fileName,
      file_size: fileSize,
      file_extension: fileExtension,
      storage_path: storagePath,
      tags: tags.filter(tag => tag.trim()),
      uploader_openid: openid,
      upload_time: new Date(),
      is_active: false, // 需要审核后才能激活
      download_count: 0,
      view_count: 0,
      favorite_count: 0,
      status: 'pending', // pending, approved, rejected
      createTime: new Date()
    }

    // 尝试生成预览图
    try {
      const previewResult = await generatePreviewImages(fileId, fileExtension)
      if (previewResult.success) {
        materialData.preview_images = previewResult.images
        materialData.has_preview = true
      }
    } catch (previewError) {
      console.error('生成预览图失败:', previewError)
      // 预览图生成失败不影响上传
    }

    // 保存到数据库
    const result = await db.collection('materials').add({
      data: materialData
    })

    if (!result._id) {
      throw new Error('保存资料记录失败')
    }

    // 记录上传日志
    await db.collection('upload_log').add({
      data: {
        material_id: result._id,
        uploader_openid: openid,
        file_name: fileName,
        file_size: fileSize,
        upload_time: new Date(),
        status: 'success'
      }
    })

    return {
      success: true,
      message: '文件上传成功，等待审核',
      data: {
        materialId: result._id,
        storagePath: storagePath,
        hasPreview: materialData.has_preview || false
      }
    }

  } catch (error) {
    console.error('文件上传失败:', error)
    
    // 记录错误日志
    try {
      await db.collection('upload_log').add({
        data: {
          uploader_openid: openid,
          file_name: fileName || 'unknown',
          file_size: fileSize || 0,
          upload_time: new Date(),
          status: 'failed',
          error_message: error.message
        }
      })
    } catch (logError) {
      console.error('记录错误日志失败:', logError)
    }

    return {
      success: false,
      message: '文件上传失败，请稍后重试'
    }
  }
}

// 获取系统配置
async function getConfig() {
  try {
    const result = await db.collection('config').doc('main').get()
    return result.data || {}
  } catch (error) {
    console.error('获取配置失败:', error)
    return {
      default_material_points: 100,
      min_material_points: 1,
      max_material_points: 1000
    }
  }
}

// 生成预览图
async function generatePreviewImages(fileId, fileExtension) {
  try {
    // 对于PDF文件，尝试生成预览图
    if (fileExtension === '.pdf') {
      // 这里应该调用PDF处理服务
      // 由于微信云开发的限制，这里只是模拟
      return {
        success: true,
        images: [
          `${fileId}_preview_1.jpg`,
          `${fileId}_preview_2.jpg`,
          `${fileId}_preview_3.jpg`
        ]
      }
    }
    
    // 对于其他文件类型，暂不生成预览图
    return {
      success: false,
      message: '该文件类型不支持预览图生成'
    }
    
  } catch (error) {
    console.error('生成预览图失败:', error)
    return {
      success: false,
      message: error.message
    }
  }
}
