<!--pages/deploy-check/deploy-check.wxml-->
<view class="container">
  <!-- 环境信息 -->
  <view class="env-info">
    <view class="env-title">🌍 当前环境信息</view>
    <view wx:if="{{envConfig}}" class="env-details">
      <view class="env-item">
        <text class="env-label">环境:</text>
        <text class="env-value">{{envConfig.currentEnv}}</text>
      </view>
      <view class="env-item">
        <text class="env-label">云环境ID:</text>
        <text class="env-value">{{envConfig.cloudEnvId}}</text>
      </view>
      <view class="env-item">
        <text class="env-label">调试模式:</text>
        <text class="env-value">{{envConfig.debug ? '开启' : '关闭'}}</text>
      </view>
    </view>
  </view>

  <!-- 检查按钮 -->
  <view class="check-section">
    <button class="check-btn" 
            bindtap="onStartCheck" 
            disabled="{{checking}}"
            loading="{{checking}}">
      {{checking ? '检查中...' : '开始检查'}}
    </button>
    
    <button class="guide-btn" bindtap="onViewGuide">
      查看部署指南
    </button>
  </view>

  <!-- 检查结果 -->
  <view wx:if="{{!checking && allPassed}}" class="result-success">
    <text>🎉 所有检查通过！您的小程序已准备就绪</text>
  </view>

  <!-- 云函数检查 -->
  <view class="check-group">
    <view class="group-title">☁️ 云函数检查</view>
    <view class="check-list">
      <view wx:for="{{cloudFunctions}}" wx:key="name" class="check-item {{item.status}}">
        <view class="item-header">
          <text class="item-icon">{{item.status === 'success' ? '✅' : item.status === 'failed' ? '❌' : item.status === 'checking' ? '🔄' : '⏳'}}</text>
          <text class="item-name">{{item.name}}</text>
          <text class="item-desc">{{item.description}}</text>
        </view>
        
        <view wx:if="{{item.message}}" class="item-message">
          <text>{{item.message}}</text>
        </view>
        
        <view wx:if="{{item.status === 'failed'}}" class="item-actions">
          <button class="retry-btn" 
                  data-index="{{index}}" 
                  bindtap="onRetryFunction">
            重试
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据库检查 -->
  <view class="check-group">
    <view class="group-title">🗄️ 数据库检查</view>
    <view class="check-list">
      <view wx:for="{{databases}}" wx:key="name" class="check-item {{item.status}}">
        <view class="item-header">
          <text class="item-icon">{{item.status === 'success' ? '✅' : item.status === 'failed' ? '❌' : item.status === 'checking' ? '🔄' : '⏳'}}</text>
          <text class="item-name">{{item.name}}</text>
          <text class="item-desc">{{item.description}}</text>
        </view>
        
        <view wx:if="{{item.message}}" class="item-message">
          <text>{{item.message}}</text>
        </view>
        
        <view wx:if="{{item.status === 'failed'}}" class="item-actions">
          <button class="retry-btn" 
                  data-index="{{index}}" 
                  bindtap="onRetryDatabase">
            重试
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 部署提示 -->
  <view class="deploy-tips">
    <view class="tips-title">💡 部署提示</view>
    <view class="tips-content">
      <text>1. 确保所有云函数已上传并部署</text>
      <text>2. 检查数据库集合是否已创建</text>
      <text>3. 验证数据库权限设置是否正确</text>
      <text>4. 如有问题，请查看部署指南</text>
    </view>
  </view>
</view>
