# PowerShell 批量部署脚本 - 2025年最新版
# 基于 cloudbase CLI 工具

param(
    [int]$Priority = 0,
    [switch]$DryRun = $false,
    [switch]$Help = $false
)

# 配置信息
$envId = "cloud1-8gm001v7fd56ff43"
$appId = "wxdcb01784f343322b"

# 云函数列表（按优先级排序）
$cloudFunctions = @(
    # 第一批：核心功能
    @{ Name = "initDatabase"; Priority = 1; Description = "数据库初始化" },
    @{ Name = "getCategories"; Priority = 1; Description = "获取分类数据" },
    @{ Name = "getRecommendMaterials"; Priority = 1; Description = "获取推荐资料" },
    @{ Name = "login"; Priority = 1; Description = "用户登录" },
    @{ Name = "searchMaterials"; Priority = 1; Description = "搜索资料" },
    
    # 第二批：基础功能
    @{ Name = "getMaterialDetail"; Priority = 2; Description = "获取资料详情" },
    @{ Name = "downloadMaterial"; Priority = 2; Description = "下载资料" },
    @{ Name = "manageFavorite"; Priority = 2; Description = "管理收藏" },
    @{ Name = "getMyDownloads"; Priority = 2; Description = "获取我的下载" },
    @{ Name = "getMyFavorites"; Priority = 2; Description = "获取我的收藏" },
    
    # 第三批：高级功能
    @{ Name = "generateShareCode"; Priority = 3; Description = "生成分享码" },
    @{ Name = "handleShareInvite"; Priority = 3; Description = "处理分享邀请" },
    @{ Name = "getShareStats"; Priority = 3; Description = "获取分享统计" },
    @{ Name = "recordShareClick"; Priority = 3; Description = "记录分享点击" },
    @{ Name = "uploadMaterial"; Priority = 3; Description = "文件上传" },
    @{ Name = "validateFile"; Priority = 3; Description = "文件验证" },
    @{ Name = "submitFeedback"; Priority = 3; Description = "提交反馈" },
    @{ Name = "getAnnouncements"; Priority = 3; Description = "获取公告" },
    @{ Name = "checkAdminPermission"; Priority = 3; Description = "管理员权限检查" },
    @{ Name = "getAdminStats"; Priority = 3; Description = "获取管理统计" }
)

# 显示帮助信息
function Show-Help {
    Write-Host "🚀 微信小程序云函数批量部署工具 - 2025年最新版" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\batch-deploy.ps1                    # 部署所有云函数"
    Write-Host "  .\batch-deploy.ps1 -Priority 1        # 仅部署优先级1的云函数"
    Write-Host "  .\batch-deploy.ps1 -DryRun            # 预演模式，不实际部署"
    Write-Host "  .\batch-deploy.ps1 -Help              # 显示此帮助信息"
    Write-Host ""
    Write-Host "参数说明:" -ForegroundColor Yellow
    Write-Host "  -Priority <1|2|3>  指定部署优先级（1=核心功能，2=基础功能，3=高级功能）"
    Write-Host "  -DryRun            预演模式，显示将要部署的函数但不实际执行"
    Write-Host "  -Help              显示帮助信息"
    Write-Host ""
    Write-Host "优先级说明:" -ForegroundColor Yellow
    Write-Host "  优先级1 (核心功能): 必须先部署的基础功能"
    Write-Host "  优先级2 (基础功能): 常用的业务功能"
    Write-Host "  优先级3 (高级功能): 管理和统计功能"
}

# 检查环境
function Test-Environment {
    Write-Host "🔍 检查部署环境..." -ForegroundColor Cyan
    
    # 检查 cloudbase CLI
    try {
        $version = cloudbase --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ cloudbase CLI 已安装: $version" -ForegroundColor Green
        } else {
            throw "cloudbase CLI 未安装"
        }
    }
    catch {
        Write-Host "❌ cloudbase CLI 未安装，请先执行: npm install -g @cloudbase/cli" -ForegroundColor Red
        exit 1
    }
    
    # 检查登录状态
    try {
        $authList = cloudbase auth list 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 已登录 cloudbase 账号" -ForegroundColor Green
        } else {
            throw "未登录"
        }
    }
    catch {
        Write-Host "❌ 未登录 cloudbase 账号，请先执行: cloudbase login" -ForegroundColor Red
        exit 1
    }
    
    # 检查云函数目录
    if (Test-Path "cloudfunctions") {
        Write-Host "✅ cloudfunctions 目录存在" -ForegroundColor Green
    } else {
        Write-Host "❌ 未找到 cloudfunctions 目录" -ForegroundColor Red
        exit 1
    }
    
    # 检查云函数文件
    $missingFunctions = @()
    foreach ($func in $cloudFunctions) {
        $funcPath = "cloudfunctions\$($func.Name)"
        if (-not (Test-Path $funcPath)) {
            $missingFunctions += $func.Name
        }
    }
    
    if ($missingFunctions.Count -gt 0) {
        Write-Host "❌ 缺少以下云函数: $($missingFunctions -join ', ')" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ 所有 $($cloudFunctions.Count) 个云函数文件存在" -ForegroundColor Green
}

# 部署单个云函数
function Deploy-Function {
    param($FunctionInfo)
    
    $name = $FunctionInfo.Name
    $description = $FunctionInfo.Description
    
    Write-Host "🚀 正在部署: $name ($description)" -ForegroundColor Yellow
    
    try {
        $result = cloudbase functions deploy $name -e $envId 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $name 部署成功" -ForegroundColor Green
            return @{ Success = $true; Name = $name; Result = $result }
        } else {
            throw "部署命令执行失败: $result"
        }
    }
    catch {
        Write-Host "❌ $name 部署失败: $_" -ForegroundColor Red
        return @{ Success = $false; Name = $name; Error = $_.ToString() }
    }
}

# 主函数
function Main {
    # 显示帮助
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Host "🚀 微信小程序云函数自动化部署工具" -ForegroundColor Cyan
    Write-Host "📅 基于 2025年最新 cloudbase CLI" -ForegroundColor Cyan
    Write-Host ""
    
    # 检查环境
    Test-Environment
    
    # 过滤要部署的函数
    $functionsToDeploy = $cloudFunctions
    if ($Priority -gt 0) {
        $functionsToDeploy = $cloudFunctions | Where-Object { $_.Priority -eq $Priority }
        Write-Host "🎯 仅部署优先级 $Priority 的云函数" -ForegroundColor Magenta
    }
    
    Write-Host "📋 准备部署 $($functionsToDeploy.Count) 个云函数..." -ForegroundColor Cyan
    Write-Host "🌍 目标环境: $envId" -ForegroundColor Cyan
    Write-Host ""
    
    # 预演模式
    if ($DryRun) {
        Write-Host "🔍 预演模式 - 不会实际部署" -ForegroundColor Magenta
        Write-Host ""
        foreach ($func in $functionsToDeploy) {
            Write-Host "  - $($func.Name) ($($func.Description))" -ForegroundColor Yellow
        }
        Write-Host ""
        Write-Host "💡 要实际部署，请移除 -DryRun 参数" -ForegroundColor Cyan
        return
    }
    
    # 开始部署
    $results = @{
        Total = $functionsToDeploy.Count
        Success = 0
        Failed = 0
        Details = @()
    }
    
    foreach ($func in $functionsToDeploy) {
        $result = Deploy-Function $func
        $results.Details += $result
        
        if ($result.Success) {
            $results.Success++
        } else {
            $results.Failed++
        }
        
        # 添加延迟避免请求过快
        Start-Sleep -Seconds 2
    }
    
    # 显示结果
    Write-Host ""
    Write-Host "📊 部署结果统计:" -ForegroundColor Cyan
    Write-Host "📦 总计: $($results.Total) 个" -ForegroundColor Blue
    Write-Host "✅ 成功: $($results.Success) 个" -ForegroundColor Green
    Write-Host "❌ 失败: $($results.Failed) 个" -ForegroundColor Red
    
    if ($results.Failed -gt 0) {
        Write-Host ""
        Write-Host "❌ 失败的云函数:" -ForegroundColor Red
        $results.Details | Where-Object { -not $_.Success } | ForEach-Object {
            Write-Host "  - $($_.Name): $($_.Error)" -ForegroundColor Red
        }
    }
    
    if ($results.Success -gt 0) {
        Write-Host ""
        Write-Host "✅ 成功的云函数:" -ForegroundColor Green
        $results.Details | Where-Object { $_.Success } | ForEach-Object {
            Write-Host "  - $($_.Name)" -ForegroundColor Green
        }
    }
    
    Write-Host ""
    Write-Host "🎉 批量部署完成!" -ForegroundColor Green
    
    if ($results.Success -gt 0) {
        Write-Host ""
        Write-Host "🔍 建议接下来:" -ForegroundColor Cyan
        Write-Host "1. 运行小程序验证云函数状态" -ForegroundColor Yellow
        Write-Host "2. 执行数据库初始化: 调用 initDatabase 云函数" -ForegroundColor Yellow
        Write-Host "3. 测试核心功能是否正常工作" -ForegroundColor Yellow
    }
}

# 执行主函数
Main
