/**
 * 模拟数据文件
 * 用于开发环境测试，当云函数不可用时提供模拟数据
 */

// 模拟分类数据
const mockCategories = [
  {
    _id: 'grade',
    name: '年级',
    options: [
      { id: 'grade-1', name: '一年级', is_active: true, sort_order: 1 },
      { id: 'grade-2', name: '二年级', is_active: true, sort_order: 2 },
      { id: 'grade-3', name: '三年级', is_active: true, sort_order: 3 },
      { id: 'grade-4', name: '四年级', is_active: true, sort_order: 4 },
      { id: 'grade-5', name: '五年级', is_active: true, sort_order: 5 },
      { id: 'grade-6', name: '六年级', is_active: true, sort_order: 6 }
    ]
  },
  {
    _id: 'subject',
    name: '科目',
    options: [
      { id: 'chinese', name: '语文', is_active: true, sort_order: 1 },
      { id: 'math', name: '数学', is_active: true, sort_order: 2 },
      { id: 'english', name: '英语', is_active: true, sort_order: 3 },
      { id: 'science', name: '科学', is_active: true, sort_order: 4 },
      { id: 'art', name: '美术', is_active: true, sort_order: 5 },
      { id: 'music', name: '音乐', is_active: true, sort_order: 6 }
    ]
  },
  {
    _id: 'semester',
    name: '上下册',
    options: [
      { id: 'first', name: '上册', is_active: true, sort_order: 1 },
      { id: 'second', name: '下册', is_active: true, sort_order: 2 },
      { id: 'whole', name: '全册', is_active: true, sort_order: 3 }
    ]
  },
  {
    _id: 'textbook',
    name: '教材版本',
    options: [
      { id: 'renjiao', name: '人教版', is_active: true, sort_order: 1 },
      { id: 'beijing', name: '北师大版', is_active: true, sort_order: 2 },
      { id: 'sujiao', name: '苏教版', is_active: true, sort_order: 3 },
      { id: 'xianggang', name: '沪教版', is_active: true, sort_order: 4 }
    ]
  }
]

// 模拟推荐资料数据
const mockRecommendMaterials = [
  {
    _id: 'material-1',
    title: '三年级数学上册期末复习资料',
    description: '包含重点知识点总结、典型例题解析和练习题',
    grade: 'grade-3',
    subject: 'math',
    semester: 'first',
    textbook: 'renjiao',
    type: 'document',
    points: 50,
    download_count: 1250,
    favorite_count: 89,
    view_count: 3420,
    file_size: 2048000,
    preview_images: [
      '/images/preview-1.jpg',
      '/images/preview-2.jpg'
    ],
    tags: ['期末复习', '重点知识', '练习题'],
    createTime: '2024-12-01T10:00:00.000Z',
    is_active: true
  },
  {
    _id: 'material-2',
    title: '四年级语文阅读理解专项训练',
    description: '精选优质阅读材料，提升阅读理解能力',
    grade: 'grade-4',
    subject: 'chinese',
    semester: 'second',
    textbook: 'renjiao',
    type: 'document',
    points: 30,
    download_count: 890,
    favorite_count: 67,
    view_count: 2150,
    file_size: 1536000,
    preview_images: [
      '/images/preview-3.jpg'
    ],
    tags: ['阅读理解', '专项训练', '能力提升'],
    createTime: '2024-11-28T14:30:00.000Z',
    is_active: true
  },
  {
    _id: 'material-3',
    title: '五年级英语单词记忆卡片',
    description: '图文并茂的单词卡片，帮助学生快速记忆',
    grade: 'grade-5',
    subject: 'english',
    semester: 'whole',
    textbook: 'renjiao',
    type: 'document',
    points: 20,
    download_count: 1560,
    favorite_count: 123,
    view_count: 4280,
    file_size: 3072000,
    preview_images: [
      '/images/preview-4.jpg',
      '/images/preview-5.jpg',
      '/images/preview-6.jpg'
    ],
    tags: ['单词记忆', '图文并茂', '快速记忆'],
    createTime: '2024-11-25T09:15:00.000Z',
    is_active: true
  }
]

// 模拟用户信息
const mockUserInfo = {
  _id: 'user-demo',
  _openid: 'demo-openid',
  nickName: '演示用户',
  avatarUrl: '/images/default-avatar.svg',
  points: 150,
  grade: 'grade-3',
  interests: ['数学', '语文'],
  createTime: '2024-10-01T08:00:00.000Z',
  lastActiveTime: new Date().toISOString(),
  download_count: 5,
  favorite_count: 3,
  share_count: 2
}

// 模拟我的下载记录
const mockMyDownloads = [
  {
    _id: 'download-1',
    material_id: 'material-1',
    material_title: '三年级数学上册期末复习资料',
    download_time: '2024-12-02T10:30:00.000Z',
    points_cost: 50,
    file_size: 2048000
  },
  {
    _id: 'download-2',
    material_id: 'material-2',
    material_title: '四年级语文阅读理解专项训练',
    download_time: '2024-12-01T15:20:00.000Z',
    points_cost: 30,
    file_size: 1536000
  }
]

// 模拟我的收藏记录
const mockMyFavorites = [
  {
    _id: 'favorite-1',
    material_id: 'material-1',
    material_title: '三年级数学上册期末复习资料',
    material_grade: 'grade-3',
    material_subject: 'math',
    material_points: 50,
    favorite_time: '2024-12-02T10:00:00.000Z'
  },
  {
    _id: 'favorite-2',
    material_id: 'material-3',
    material_title: '五年级英语单词记忆卡片',
    material_grade: 'grade-5',
    material_subject: 'english',
    material_points: 20,
    favorite_time: '2024-12-01T16:45:00.000Z'
  }
]

// 模拟积分记录
const mockPointsLog = [
  {
    _id: 'points-1',
    amount: -50,
    balance: 150,
    type: 'download',
    description: '下载资料：三年级数学上册期末复习资料',
    createTime: '2024-12-02T10:30:00.000Z'
  },
  {
    _id: 'points-2',
    amount: 30,
    balance: 200,
    type: 'share_reward',
    description: '邀请新用户奖励',
    createTime: '2024-12-01T14:20:00.000Z'
  },
  {
    _id: 'points-3',
    amount: 100,
    balance: 170,
    type: 'register',
    description: '新用户注册奖励',
    createTime: '2024-10-01T08:00:00.000Z'
  }
]

// 检查是否需要使用模拟数据
function isDevelopment() {
  try {
    const accountInfo = wx.getAccountInfoSync()
    // 只在开发工具中且没有配置云环境时使用模拟数据
    return accountInfo.miniProgram.envVersion === 'develop' && !hasCloudEnv()
  } catch (error) {
    return false // 默认不使用模拟数据
  }
}

// 检查是否已配置云环境
function hasCloudEnv() {
  try {
    // 检查是否已初始化云开发
    return wx.cloud && wx.cloud.database
  } catch (error) {
    return false
  }
}

module.exports = {
  mockCategories,
  mockRecommendMaterials,
  mockUserInfo,
  mockMyDownloads,
  mockMyFavorites,
  mockPointsLog,
  isDevelopment
}
