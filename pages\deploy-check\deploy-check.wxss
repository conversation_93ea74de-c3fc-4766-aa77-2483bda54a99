/* pages/deploy-check/deploy-check.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* 环境信息 */
.env-info {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.env-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.env-details {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.env-item {
  display: flex;
  align-items: center;
}

.env-label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.env-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 检查按钮区域 */
.check-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.check-btn {
  flex: 1;
  background-color: #FF6B35;
  color: white;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  border: none;
}

.check-btn[disabled] {
  background-color: #ccc;
}

.guide-btn {
  background-color: #1890ff;
  color: white;
  border-radius: 50rpx;
  padding: 30rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

/* 检查结果 */
.result-success {
  background-color: #f6ffed;
  border: 1rpx solid #b7eb8f;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.result-success text {
  font-size: 32rpx;
  color: #52c41a;
  font-weight: 600;
}

/* 检查组 */
.check-group {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.group-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

/* 检查列表 */
.check-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.check-item {
  background-color: #f8f8f8;
  border-radius: 15rpx;
  padding: 25rpx;
  border-left: 4rpx solid #ddd;
  transition: all 0.3s ease;
}

.check-item.success {
  border-left-color: #52c41a;
  background-color: #f6ffed;
}

.check-item.failed {
  border-left-color: #ff4d4f;
  background-color: #fff2f0;
}

.check-item.checking {
  border-left-color: #1890ff;
  background-color: #e6f7ff;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.item-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.item-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-right: 20rpx;
  min-width: 200rpx;
}

.item-desc {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.item-message {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 10rpx;
  padding: 15rpx;
  margin-bottom: 10rpx;
}

.item-message text {
  font-size: 24rpx;
  color: #666;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
}

.retry-btn {
  background-color: #ff4d4f;
  color: white;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
  border: none;
}

/* 部署提示 */
.deploy-tips {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.tips-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.tips-content text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 动画效果 */
.check-item.checking .item-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
