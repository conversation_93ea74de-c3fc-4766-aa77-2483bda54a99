// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const { fileId, fileName, fileSize } = event

  try {
    if (!fileId || !fileName) {
      return {
        success: false,
        message: '缺少文件信息'
      }
    }

    // 文件大小验证
    const maxSize = 50 * 1024 * 1024 // 50MB
    if (fileSize > maxSize) {
      return {
        success: false,
        message: '文件大小不能超过50MB',
        code: 'FILE_TOO_LARGE'
      }
    }

    // 文件格式验证
    const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
    const allowedFormats = {
      '.pdf': {
        type: 'document',
        name: 'PDF文档',
        maxSize: 50 * 1024 * 1024,
        supportPreview: true
      },
      '.doc': {
        type: 'document',
        name: 'Word文档',
        maxSize: 20 * 1024 * 1024,
        supportPreview: false
      },
      '.docx': {
        type: 'document',
        name: 'Word文档',
        maxSize: 20 * 1024 * 1024,
        supportPreview: false
      },
      '.ppt': {
        type: 'presentation',
        name: 'PowerPoint演示文稿',
        maxSize: 30 * 1024 * 1024,
        supportPreview: false
      },
      '.pptx': {
        type: 'presentation',
        name: 'PowerPoint演示文稿',
        maxSize: 30 * 1024 * 1024,
        supportPreview: false
      },
      '.xls': {
        type: 'spreadsheet',
        name: 'Excel表格',
        maxSize: 10 * 1024 * 1024,
        supportPreview: false
      },
      '.xlsx': {
        type: 'spreadsheet',
        name: 'Excel表格',
        maxSize: 10 * 1024 * 1024,
        supportPreview: false
      }
    }

    if (!allowedFormats[fileExtension]) {
      return {
        success: false,
        message: '不支持的文件格式',
        code: 'UNSUPPORTED_FORMAT',
        supportedFormats: Object.keys(allowedFormats)
      }
    }

    const formatInfo = allowedFormats[fileExtension]

    // 检查特定格式的大小限制
    if (fileSize > formatInfo.maxSize) {
      return {
        success: false,
        message: `${formatInfo.name}文件大小不能超过${Math.round(formatInfo.maxSize / 1024 / 1024)}MB`,
        code: 'FORMAT_SIZE_LIMIT'
      }
    }

    // 文件名验证
    const fileNameValidation = validateFileName(fileName)
    if (!fileNameValidation.valid) {
      return {
        success: false,
        message: fileNameValidation.message,
        code: 'INVALID_FILENAME'
      }
    }

    // 尝试获取文件信息进行进一步验证
    try {
      const fileInfo = await cloud.getTempFileURL({
        fileList: [fileId]
      })

      if (!fileInfo.fileList || fileInfo.fileList.length === 0) {
        return {
          success: false,
          message: '无法获取文件信息',
          code: 'FILE_NOT_FOUND'
        }
      }

      const file = fileInfo.fileList[0]
      if (file.status !== 0) {
        return {
          success: false,
          message: '文件访问失败',
          code: 'FILE_ACCESS_ERROR'
        }
      }

    } catch (fileError) {
      console.error('获取文件信息失败:', fileError)
      return {
        success: false,
        message: '文件验证失败',
        code: 'FILE_VALIDATION_ERROR'
      }
    }

    // 生成文件元数据
    const metadata = {
      originalName: fileName,
      extension: fileExtension,
      type: formatInfo.type,
      typeName: formatInfo.name,
      size: fileSize,
      sizeFormatted: formatFileSize(fileSize),
      supportPreview: formatInfo.supportPreview,
      uploadTime: new Date().toISOString()
    }

    return {
      success: true,
      message: '文件验证通过',
      data: {
        fileId,
        metadata,
        recommendations: generateRecommendations(formatInfo, fileSize)
      }
    }

  } catch (error) {
    console.error('文件验证失败:', error)
    return {
      success: false,
      message: '文件验证失败，请稍后重试',
      code: 'VALIDATION_ERROR'
    }
  }
}

// 验证文件名
function validateFileName(fileName) {
  // 检查文件名长度
  if (fileName.length > 100) {
    return {
      valid: false,
      message: '文件名长度不能超过100个字符'
    }
  }

  // 检查非法字符
  const invalidChars = /[<>:"/\\|?*]/
  if (invalidChars.test(fileName)) {
    return {
      valid: false,
      message: '文件名包含非法字符'
    }
  }

  // 检查是否为空或只有空格
  if (!fileName.trim()) {
    return {
      valid: false,
      message: '文件名不能为空'
    }
  }

  return {
    valid: true,
    message: '文件名验证通过'
  }
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生成建议
function generateRecommendations(formatInfo, fileSize) {
  const recommendations = []

  // 文件大小建议
  if (fileSize > formatInfo.maxSize * 0.8) {
    recommendations.push({
      type: 'warning',
      message: '文件较大，建议压缩后上传以提高下载速度'
    })
  }

  // 格式建议
  if (formatInfo.type === 'document' && !formatInfo.supportPreview) {
    recommendations.push({
      type: 'info',
      message: '建议转换为PDF格式以支持在线预览'
    })
  }

  // 通用建议
  recommendations.push({
    type: 'tip',
    message: '请确保文件内容清晰，标题和描述准确'
  })

  return recommendations
}
