// pages/deploy-tracker/deploy-tracker.js
// 部署进度跟踪页面

Page({
  data: {
    cloudFunctions: [
      // 第一批：核心功能
      { name: 'initDatabase', priority: 1, status: 'pending', description: '数据库初始化', category: '核心功能' },
      { name: 'getCategories', priority: 1, status: 'pending', description: '获取分类数据', category: '核心功能' },
      { name: 'getRecommendMaterials', priority: 1, status: 'pending', description: '获取推荐资料', category: '核心功能' },
      { name: 'login', priority: 1, status: 'pending', description: '用户登录', category: '核心功能' },
      { name: 'searchMaterials', priority: 1, status: 'pending', description: '搜索资料', category: '核心功能' },
      
      // 第二批：基础功能
      { name: 'getMaterialDetail', priority: 2, status: 'pending', description: '获取资料详情', category: '基础功能' },
      { name: 'downloadMaterial', priority: 2, status: 'pending', description: '下载资料', category: '基础功能' },
      { name: 'manageFavorite', priority: 2, status: 'pending', description: '管理收藏', category: '基础功能' },
      { name: 'getMyDownloads', priority: 2, status: 'pending', description: '获取我的下载', category: '基础功能' },
      { name: 'getMyFavorites', priority: 2, status: 'pending', description: '获取我的收藏', category: '基础功能' },
      
      // 第三批：高级功能
      { name: 'generateShareCode', priority: 3, status: 'pending', description: '生成分享码', category: '高级功能' },
      { name: 'handleShareInvite', priority: 3, status: 'pending', description: '处理分享邀请', category: '高级功能' },
      { name: 'getShareStats', priority: 3, status: 'pending', description: '获取分享统计', category: '高级功能' },
      { name: 'recordShareClick', priority: 3, status: 'pending', description: '记录分享点击', category: '高级功能' },
      { name: 'uploadMaterial', priority: 3, status: 'pending', description: '文件上传', category: '高级功能' },
      { name: 'validateFile', priority: 3, status: 'pending', description: '文件验证', category: '高级功能' },
      { name: 'submitFeedback', priority: 3, status: 'pending', description: '提交反馈', category: '高级功能' },
      { name: 'getAnnouncements', priority: 3, status: 'pending', description: '获取公告', category: '高级功能' },
      { name: 'checkAdminPermission', priority: 3, status: 'pending', description: '管理员权限检查', category: '高级功能' },
      { name: 'getAdminStats', priority: 3, status: 'pending', description: '获取管理统计', category: '高级功能' }
    ],
    
    deployStats: {
      total: 20,
      deployed: 0,
      failed: 0,
      pending: 20
    },
    
    currentPriority: 1,
    checking: false,
    autoCheck: false,
    checkInterval: null
  },

  onLoad() {
    this.checkAllFunctions()
  },

  onUnload() {
    // 清理定时器
    if (this.data.checkInterval) {
      clearInterval(this.data.checkInterval)
    }
  },

  // 检查所有云函数状态
  async checkAllFunctions() {
    this.setData({ checking: true })
    
    const functions = this.data.cloudFunctions
    let deployed = 0, failed = 0, pending = 0
    
    for (let i = 0; i < functions.length; i++) {
      const func = functions[i]
      
      try {
        // 尝试调用云函数
        const result = await wx.cloud.callFunction({
          name: func.name,
          data: {}
        })
        
        if (result && (result.result || result.errMsg === 'cloud.callFunction:ok')) {
          func.status = 'deployed'
          func.lastCheck = new Date().toLocaleTimeString()
          deployed++
        } else {
          func.status = 'failed'
          func.error = '调用失败'
          failed++
        }
      } catch (error) {
        if (error.errMsg && error.errMsg.includes('cloud function not found')) {
          func.status = 'pending'
          pending++
        } else {
          func.status = 'failed'
          func.error = error.message
          failed++
        }
      }
      
      // 更新进度
      this.setData({
        cloudFunctions: functions,
        deployStats: { total: 20, deployed, failed, pending }
      })
      
      // 添加小延迟避免请求过快
      await this.delay(200)
    }
    
    this.setData({ checking: false })
    
    // 显示检查结果
    const { deployed: d, failed: f, pending: p } = this.data.deployStats
    wx.showToast({
      title: `已部署:${d} 失败:${f} 待部署:${p}`,
      icon: 'none',
      duration: 3000
    })
  },

  // 检查单个云函数
  async checkSingleFunction(e) {
    const { index } = e.currentTarget.dataset
    const functions = this.data.cloudFunctions
    const func = functions[index]
    
    func.status = 'checking'
    this.setData({ cloudFunctions: functions })
    
    try {
      const result = await wx.cloud.callFunction({
        name: func.name,
        data: {}
      })
      
      if (result && (result.result || result.errMsg === 'cloud.callFunction:ok')) {
        func.status = 'deployed'
        func.lastCheck = new Date().toLocaleTimeString()
        wx.showToast({
          title: '部署成功',
          icon: 'success'
        })
      } else {
        func.status = 'failed'
        func.error = '调用失败'
        wx.showToast({
          title: '调用失败',
          icon: 'error'
        })
      }
    } catch (error) {
      if (error.errMsg && error.errMsg.includes('cloud function not found')) {
        func.status = 'pending'
        wx.showToast({
          title: '函数未部署',
          icon: 'none'
        })
      } else {
        func.status = 'failed'
        func.error = error.message
        wx.showToast({
          title: '检查失败',
          icon: 'error'
        })
      }
    }
    
    this.setData({ cloudFunctions: functions })
    this.updateStats()
  },

  // 更新统计数据
  updateStats() {
    const functions = this.data.cloudFunctions
    const deployed = functions.filter(f => f.status === 'deployed').length
    const failed = functions.filter(f => f.status === 'failed').length
    const pending = functions.filter(f => f.status === 'pending').length
    
    this.setData({
      deployStats: { total: 20, deployed, failed, pending }
    })
  },

  // 开启/关闭自动检查
  toggleAutoCheck() {
    const autoCheck = !this.data.autoCheck
    
    if (autoCheck) {
      // 开启自动检查，每30秒检查一次
      const interval = setInterval(() => {
        this.checkAllFunctions()
      }, 30000)
      
      this.setData({
        autoCheck: true,
        checkInterval: interval
      })
      
      wx.showToast({
        title: '已开启自动检查',
        icon: 'success'
      })
    } else {
      // 关闭自动检查
      if (this.data.checkInterval) {
        clearInterval(this.data.checkInterval)
      }
      
      this.setData({
        autoCheck: false,
        checkInterval: null
      })
      
      wx.showToast({
        title: '已关闭自动检查',
        icon: 'none'
      })
    }
  },

  // 查看部署指南
  onViewGuide() {
    wx.showModal({
      title: '部署指南',
      content: '1. 右键点击云函数文件夹\n2. 选择"上传并部署：云端安装依赖"\n3. 等待部署完成\n4. 点击"刷新状态"验证\n\n详细指南请查看 deploy/DEPLOY-NOW-2025.md',
      showCancel: false
    })
  },

  // 查看函数详情
  onViewDetails(e) {
    const { index } = e.currentTarget.dataset
    const func = this.data.cloudFunctions[index]
    
    let content = `函数名: ${func.name}\n描述: ${func.description}\n状态: ${this.getStatusText(func.status)}\n优先级: ${func.priority}`
    
    if (func.lastCheck) {
      content += `\n最后检查: ${func.lastCheck}`
    }
    
    if (func.error) {
      content += `\n错误信息: ${func.error}`
    }
    
    wx.showModal({
      title: '函数详情',
      content: content,
      showCancel: false
    })
  },

  // 复制函数名
  onCopyFunctionName(e) {
    const { index } = e.currentTarget.dataset
    const func = this.data.cloudFunctions[index]
    
    wx.setClipboardData({
      data: func.name,
      success: () => {
        wx.showToast({
          title: '已复制函数名',
          icon: 'success'
        })
      }
    })
  },

  // 获取状态文本
  getStatusText(status) {
    switch (status) {
      case 'deployed':
        return '已部署'
      case 'failed':
        return '失败'
      case 'checking':
        return '检查中'
      case 'pending':
      default:
        return '待部署'
    }
  },

  // 获取状态图标
  getStatusIcon(status) {
    switch (status) {
      case 'deployed':
        return '✅'
      case 'failed':
        return '❌'
      case 'checking':
        return '🔄'
      case 'pending':
      default:
        return '⏳'
    }
  },

  // 获取优先级颜色
  getPriorityColor(priority) {
    switch (priority) {
      case 1:
        return '#ff4d4f' // 红色 - 核心功能
      case 2:
        return '#fa8c16' // 橙色 - 基础功能
      case 3:
        return '#1890ff' // 蓝色 - 高级功能
      default:
        return '#666'
    }
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
})
