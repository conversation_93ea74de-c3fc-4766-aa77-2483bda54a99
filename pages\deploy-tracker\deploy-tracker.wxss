/* pages/deploy-tracker/deploy-tracker.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  margin-bottom: 20rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
}

/* 统计信息 */
.stats-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stats-card {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #FF6B35;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 进度条 */
.progress-section {
  margin-top: 30rpx;
}

.progress-bar {
  width: 100%;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.progress-fill {
  height: 100%;
  background-color: #52c41a;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  display: block;
}

/* 操作按钮 */
.action-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  width: 100%;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  border: none;
  margin-bottom: 20rpx;
}

.action-btn.primary {
  background-color: #FF6B35;
  color: white;
  font-weight: 600;
}

.action-btn.secondary {
  background-color: #f0f0f0;
  color: #666;
}

.action-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

.action-row {
  display: flex;
  gap: 20rpx;
}

.action-row .action-btn {
  flex: 1;
  margin-bottom: 0;
}

/* 云函数列表 */
.functions-section {
  margin-bottom: 20rpx;
}

.function-group {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.group-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.group-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.group-desc {
  font-size: 26rpx;
  color: #666;
}

.function-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.function-item {
  background-color: #f8f8f8;
  border-radius: 15rpx;
  padding: 25rpx;
  border-left: 4rpx solid #ddd;
  transition: all 0.3s ease;
}

.function-item.deployed {
  border-left-color: #52c41a;
  background-color: #f6ffed;
}

.function-item.failed {
  border-left-color: #ff4d4f;
  background-color: #fff2f0;
}

.function-item.checking {
  border-left-color: #1890ff;
  background-color: #e6f7ff;
}

.function-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.function-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.function-info {
  flex: 1;
}

.function-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.function-desc {
  font-size: 26rpx;
  color: #666;
}

.function-status {
  text-align: right;
}

.status-text {
  font-size: 24rpx;
  color: #666;
  padding: 8rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
}

.function-actions {
  display: flex;
  gap: 15rpx;
  margin-bottom: 15rpx;
}

.action-btn-small {
  background-color: #1890ff;
  color: white;
  border-radius: 25rpx;
  padding: 15rpx 25rpx;
  font-size: 24rpx;
  border: none;
}

.function-error {
  background-color: rgba(255, 77, 79, 0.1);
  border-radius: 10rpx;
  padding: 15rpx;
  margin-bottom: 10rpx;
}

.function-error text {
  font-size: 24rpx;
  color: #ff4d4f;
}

.function-time {
  text-align: right;
}

.function-time text {
  font-size: 22rpx;
  color: #999;
}

/* 部署提示 */
.deploy-tips {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tips-content text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 动画效果 */
.function-item.checking .function-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
