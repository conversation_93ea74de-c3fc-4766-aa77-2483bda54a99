<!--pages/my-downloads/my-downloads.wxml-->
<view class="downloads-container">
  <!-- 未登录状态 -->
  <view class="login-prompt" wx:if="{{!isLogin}}">
    <image src="/images/empty-downloads.svg" mode="aspectFit"></image>
    <view class="prompt-text">
      <text>请先登录查看下载记录</text>
    </view>
    <button class="login-btn" bindtap="onGoLogin">立即登录</button>
  </view>

  <!-- 已登录状态 -->
  <view class="downloads-content" wx:if="{{isLogin}}">
    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <view class="stats-number">{{totalDownloads}}</view>
          <view class="stats-label">下载资料</view>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <view class="stats-number">{{totalPoints}}</view>
          <view class="stats-label">消耗积分</view>
        </view>
      </view>
    </view>

    <!-- 操作栏 -->
    <view class="toolbar" wx:if="{{downloads.length > 0}}">
      <view class="sort-section">
        <view class="sort-btn" bindtap="onSortTap">
          <text>{{currentSortName}}</text>
          <image src="/images/icon-arrow-down.svg" mode="aspectFit"></image>
        </view>
      </view>
      <view class="action-section">
        <view class="clear-btn" bindtap="onClearHistory">
          <text>清空记录</text>
        </view>
      </view>
    </view>

    <!-- 排序菜单 -->
    <view class="sort-menu" wx:if="{{showSortMenu}}">
      <view class="sort-mask" bindtap="onCloseSortMenu"></view>
      <view class="sort-popup">
        <view class="sort-header">
          <text>排序方式</text>
          <view class="close-btn" bindtap="onCloseSortMenu">
            <image src="/images/icon-close.svg" mode="aspectFit"></image>
          </view>
        </view>
        <view class="sort-options">
          <view
            class="sort-option {{sortType === item.value ? 'active' : ''}}"
            wx:for="{{sortOptions}}"
            wx:key="value"
            data-sort="{{item.value}}"
            bindtap="onSortSelect"
          >
            <text>{{item.name}}</text>
            <image wx:if="{{sortType === item.value}}" src="/images/icon-check.svg" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 下载列表 -->
    <view class="downloads-list" wx:if="{{!loading && downloads.length > 0}}">
      <view
        class="download-item"
        wx:for="{{downloads}}"
        wx:key="_id"
        data-download="{{item}}"
        bindtap="onDownloadTap"
      >
        <view class="download-cover">
          <image
            src="{{item.material.cover_image || '/images/default-cover.svg'}}"
            mode="aspectFill"
          ></image>
        </view>

        <view class="download-info">
          <view class="download-title">{{item.material.title}}</view>
          <view class="download-meta">
            <view class="meta-tags">
              <text class="tag tag-primary" wx:if="{{item.material.grade_name}}">{{item.material.grade_name}}</text>
              <text class="tag tag-secondary" wx:if="{{item.material.subject_name}}">{{item.material.subject_name}}</text>
            </view>
            <view class="download-time">{{item.download_time_formatted || item.download_time}}</view>
          </view>
          <view class="download-stats">
            <view class="points-cost">
              <image src="/images/icon-points.svg" mode="aspectFit"></image>
              <text>{{item.points_cost}}积分</text>
            </view>
          </view>
        </view>

        <view class="download-actions">
          <view
            class="action-btn redownload-btn"
            data-download="{{item}}"
            bindtap="onRedownload"
            catchtap=""
          >
            <image src="/images/icon-download.svg" mode="aspectFit"></image>
            <text>重新下载</text>
          </view>
          <view
            class="action-btn delete-btn"
            data-download="{{item}}"
            bindtap="onDeleteDownload"
            catchtap=""
          >
            <image src="/images/icon-close.svg" mode="aspectFit"></image>
            <text>删除</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && downloads.length > 0}}">
      <text wx:if="{{loadingMore}}">加载中...</text>
      <text wx:else>上拉加载更多</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-downloads" wx:if="{{!loading && downloads.length === 0}}">
      <image src="/images/empty-downloads.svg" mode="aspectFit"></image>
      <view class="empty-text">
        <text>暂无下载记录</text>
        <text>去下载一些资料吧</text>
      </view>
      <button class="browse-btn" bindtap="onGoBrowse">浏览资料</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>
</view>