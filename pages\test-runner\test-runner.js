// pages/test-runner/test-runner.js
const { runAPITests } = require('../../tests/api.test.js')
const { testFramework } = require('../../utils/test.js')

Page({
  data: {
    // 测试状态
    isRunning: false,
    testResults: null,
    
    // 测试选项
    testTypes: [
      { id: 'unit', name: '单元测试', enabled: true },
      { id: 'integration', name: '集成测试', enabled: true },
      { id: 'performance', name: '性能测试', enabled: true }
    ],
    
    // 日志
    logs: []
  },

  onLoad(options) {
    // 重写console.log来捕获测试日志
    this.originalConsoleLog = console.log
    console.log = (...args) => {
      this.addLog(args.join(' '))
      this.originalConsoleLog(...args)
    }
  },

  onUnload() {
    // 恢复原始console.log
    if (this.originalConsoleLog) {
      console.log = this.originalConsoleLog
    }
  },

  // 添加日志
  addLog(message) {
    const logs = this.data.logs
    logs.push({
      time: new Date().toLocaleTimeString(),
      message
    })
    
    // 限制日志数量
    if (logs.length > 100) {
      logs.shift()
    }
    
    this.setData({ logs })
  },

  // 切换测试类型
  onToggleTestType(e) {
    const { index } = e.currentTarget.dataset
    const testTypes = this.data.testTypes
    testTypes[index].enabled = !testTypes[index].enabled
    
    this.setData({ testTypes })
  },

  // 运行测试
  async onRunTests() {
    if (this.data.isRunning) return

    this.setData({
      isRunning: true,
      testResults: null,
      logs: []
    })

    try {
      this.addLog('🚀 开始运行测试...')
      
      // 运行API测试
      await runAPITests()
      
      // 获取测试报告
      const report = testFramework.getReport()
      
      this.setData({
        testResults: report
      })
      
      this.addLog('✅ 测试完成')
      
    } catch (error) {
      console.error('测试运行失败:', error)
      this.addLog(`❌ 测试运行失败: ${error.message}`)
    } finally {
      this.setData({
        isRunning: false
      })
    }
  },

  // 清空日志
  onClearLogs() {
    this.setData({
      logs: []
    })
  },

  // 导出测试报告
  onExportReport() {
    if (!this.data.testResults) {
      wx.showToast({
        title: '没有测试结果',
        icon: 'none'
      })
      return
    }

    const report = JSON.stringify(this.data.testResults, null, 2)
    
    wx.setClipboardData({
      data: report,
      success: () => {
        wx.showToast({
          title: '报告已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  },

  // 运行性能测试
  async onRunPerformanceTest() {
    this.setData({
      isRunning: true
    })

    try {
      this.addLog('🔥 开始性能测试...')
      
      // 测试页面渲染性能
      const renderStart = Date.now()
      this.setData({
        testData: new Array(100).fill().map((_, i) => ({
          id: i,
          title: `测试项目 ${i}`,
          description: `这是第 ${i} 个测试项目的描述`
        }))
      })
      const renderTime = Date.now() - renderStart
      
      this.addLog(`📊 页面渲染耗时: ${renderTime}ms`)
      
      // 测试存储性能
      const storageStart = Date.now()
      for (let i = 0; i < 10; i++) {
        wx.setStorageSync(`test_key_${i}`, `test_value_${i}`)
      }
      const storageTime = Date.now() - storageStart
      
      this.addLog(`💾 存储操作耗时: ${storageTime}ms`)
      
      // 清理测试数据
      for (let i = 0; i < 10; i++) {
        wx.removeStorageSync(`test_key_${i}`)
      }
      
      this.addLog('✅ 性能测试完成')
      
    } catch (error) {
      this.addLog(`❌ 性能测试失败: ${error.message}`)
    } finally {
      this.setData({
        isRunning: false,
        testData: []
      })
    }
  },

  // 运行兼容性测试
  async onRunCompatibilityTest() {
    this.addLog('🔧 开始兼容性测试...')
    
    const systemInfo = wx.getSystemInfoSync()
    this.addLog(`📱 设备信息: ${systemInfo.brand} ${systemInfo.model}`)
    this.addLog(`📱 系统版本: ${systemInfo.system}`)
    this.addLog(`📱 微信版本: ${systemInfo.version}`)
    this.addLog(`📱 基础库版本: ${systemInfo.SDKVersion}`)
    
    // 测试API兼容性
    const apis = [
      'chooseImage',
      'chooseVideo',
      'chooseMessageFile',
      'setClipboardData',
      'vibrateShort',
      'createRewardedVideoAd',
      'onShareAppMessage',
      'onShareTimeline'
    ]
    
    apis.forEach(api => {
      const supported = wx.canIUse(api)
      this.addLog(`${supported ? '✅' : '❌'} ${api}: ${supported ? '支持' : '不支持'}`)
    })
    
    this.addLog('✅ 兼容性测试完成')
  },

  // 格式化测试结果
  formatTestResult(result) {
    const statusIcon = result.status === 'passed' ? '✅' : '❌'
    return `${statusIcon} ${result.name} (${result.duration}ms)`
  }
})
