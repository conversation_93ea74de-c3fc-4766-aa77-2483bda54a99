// pages/upload-material/upload-material.js
const { categoryAPI } = require('../../utils/api.js')
const { showError, showSuccess, showLoading, hideLoading, showConfirm } = require('../../utils/util.js')

Page({
  data: {
    // 表单数据
    formData: {
      title: '',
      description: '',
      grade: '',
      subject: '',
      semester: '',
      textbook: '',
      type: 'document',
      points: 100,
      tags: ''
    },

    // 分类数据
    categories: [],
    gradeOptions: [],
    subjectOptions: [],
    semesterOptions: [],
    textbookOptions: [],

    // 文件信息
    selectedFile: null,
    fileValidation: null,

    // 状态
    loading: false,
    uploading: false,

    // 显示名称
    gradeName: '',
    subjectName: '',
    semesterName: '',
    textbookName: ''
  },

  onLoad(options) {
    this.initPage()
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ loading: true })
      await this.loadCategories()
    } catch (error) {
      console.error('页面初始化失败:', error)
      showError('页面加载失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载分类数据
  async loadCategories() {
    try {
      const categories = await categoryAPI.getCategories()
      
      const gradeOptions = categories.find(cat => cat._id === 'grade')?.options || []
      const subjectOptions = categories.find(cat => cat._id === 'subject')?.options || []
      const semesterOptions = categories.find(cat => cat._id === 'semester')?.options || []
      const textbookOptions = categories.find(cat => cat._id === 'textbook')?.options || []

      this.setData({
        categories,
        gradeOptions,
        subjectOptions,
        semesterOptions,
        textbookOptions
      })
    } catch (error) {
      console.error('加载分类数据失败:', error)
    }
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 选择器变化
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail

    let selectedValue = ''
    let selectedName = ''
    let options = []

    switch (field) {
      case 'grade':
        options = this.data.gradeOptions
        break
      case 'subject':
        options = this.data.subjectOptions
        break
      case 'semester':
        options = this.data.semesterOptions
        break
      case 'textbook':
        options = this.data.textbookOptions
        break
    }

    if (options[value]) {
      selectedValue = options[value].id
      selectedName = options[value].name
    }

    this.setData({
      [`formData.${field}`]: selectedValue,
      [`${field}Name`]: selectedName
    })
  },

  // 选择文件
  async onChooseFile() {
    try {
      const result = await wx.chooseMessageFile({
        count: 1,
        type: 'file',
        extension: ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx']
      })

      if (result.tempFiles && result.tempFiles.length > 0) {
        const file = result.tempFiles[0]
        
        // 验证文件
        showLoading('验证文件中...')
        const validation = await this.validateFile(file)
        hideLoading()

        if (validation.success) {
          this.setData({
            selectedFile: file,
            fileValidation: validation.data
          })
          showSuccess('文件选择成功')
        } else {
          showError(validation.message)
        }
      }
    } catch (error) {
      hideLoading()
      console.error('选择文件失败:', error)
      showError('选择文件失败')
    }
  },

  // 验证文件
  async validateFile(file) {
    try {
      // 先上传到临时存储
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: `temp/${Date.now()}_${file.name}`,
        filePath: file.path
      })

      if (!uploadResult.fileID) {
        throw new Error('文件上传失败')
      }

      // 调用验证云函数
      const result = await wx.cloud.callFunction({
        name: 'validateFile',
        data: {
          fileId: uploadResult.fileID,
          fileName: file.name,
          fileSize: file.size
        }
      })

      return result.result
    } catch (error) {
      console.error('文件验证失败:', error)
      return {
        success: false,
        message: '文件验证失败'
      }
    }
  },

  // 移除文件
  onRemoveFile() {
    this.setData({
      selectedFile: null,
      fileValidation: null
    })
  },

  // 提交表单
  async onSubmit() {
    try {
      // 验证表单
      const validation = this.validateForm()
      if (!validation.valid) {
        showError(validation.message)
        return
      }

      if (!this.data.selectedFile) {
        showError('请选择要上传的文件')
        return
      }

      const confirm = await showConfirm('确认上传资料？', '提示')
      if (!confirm) return

      this.setData({ uploading: true })
      showLoading('上传中...')

      // 上传文件到正式存储
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: `materials/${Date.now()}_${this.data.selectedFile.name}`,
        filePath: this.data.selectedFile.path
      })

      if (!uploadResult.fileID) {
        throw new Error('文件上传失败')
      }

      // 调用上传云函数
      const result = await wx.cloud.callFunction({
        name: 'uploadMaterial',
        data: {
          ...this.data.formData,
          fileId: uploadResult.fileID,
          fileName: this.data.selectedFile.name,
          fileSize: this.data.selectedFile.size,
          tags: this.data.formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
        }
      })

      if (result.result.success) {
        showSuccess('资料上传成功，等待审核')
        // 重置表单
        this.resetForm()
      } else {
        showError(result.result.message)
      }

    } catch (error) {
      console.error('上传失败:', error)
      showError('上传失败，请稍后重试')
    } finally {
      hideLoading()
      this.setData({ uploading: false })
    }
  },

  // 验证表单
  validateForm() {
    const { formData } = this.data

    if (!formData.title.trim()) {
      return { valid: false, message: '请输入资料标题' }
    }

    if (!formData.description.trim()) {
      return { valid: false, message: '请输入资料描述' }
    }

    if (!formData.grade) {
      return { valid: false, message: '请选择年级' }
    }

    if (!formData.subject) {
      return { valid: false, message: '请选择科目' }
    }

    if (formData.points < 1 || formData.points > 1000) {
      return { valid: false, message: '积分设置必须在1-1000之间' }
    }

    return { valid: true }
  },

  // 重置表单
  resetForm() {
    this.setData({
      formData: {
        title: '',
        description: '',
        grade: '',
        subject: '',
        semester: '',
        textbook: '',
        type: 'document',
        points: 100,
        tags: ''
      },
      selectedFile: null,
      fileValidation: null
    })
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
})
