# 方案二：快速批量部署脚本 - 2025年8月
# 简化版本，专注于批量部署功能

param(
    [int]$Priority = 1,
    [switch]$All = $false,
    [switch]$DryRun = $false
)

$envId = "cloud1-8gm001v7fd56ff43"

# 云函数列表（按优先级分组）
$allFunctions = @{
    1 = @(
        "initDatabase",
        "getCategories", 
        "getRecommendMaterials",
        "login",
        "searchMaterials"
    )
    2 = @(
        "getMaterialDetail",
        "downloadMaterial",
        "manageFavorite", 
        "getMyDownloads",
        "getMyFavorites"
    )
    3 = @(
        "generateShareCode",
        "handleShareInvite",
        "getShareStats",
        "recordShareClick",
        "uploadMaterial",
        "validateFile",
        "submitFeedback",
        "getAnnouncements",
        "checkAdminPermission",
        "getAdminStats"
    )
}

Write-Host "🚀 快速批量部署工具 - 方案二" -ForegroundColor Cyan
Write-Host "环境ID: $envId" -ForegroundColor Yellow
Write-Host ""

# 确定要部署的函数
$functionsToDeploy = @()
if ($All) {
    $functionsToDeploy = $allFunctions[1] + $allFunctions[2] + $allFunctions[3]
    Write-Host "📦 部署所有 20 个云函数" -ForegroundColor Magenta
} else {
    $functionsToDeploy = $allFunctions[$Priority]
    $priorityName = switch($Priority) {
        1 { "核心功能" }
        2 { "基础功能" }
        3 { "高级功能" }
    }
    Write-Host "📦 部署优先级 $Priority ($priorityName) - $($functionsToDeploy.Count) 个函数" -ForegroundColor Magenta
}

Write-Host ""

# 预演模式
if ($DryRun) {
    Write-Host "🔍 预演模式 - 将要部署的函数:" -ForegroundColor Yellow
    $functionsToDeploy | ForEach-Object { Write-Host "  - $_" -ForegroundColor Cyan }
    Write-Host ""
    Write-Host "要实际部署，请移除 -DryRun 参数" -ForegroundColor Green
    exit 0
}

# 检查基本环境
Write-Host "🔍 检查环境..." -ForegroundColor Cyan
try {
    $tcbVersion = tcb --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "tcb CLI 未安装"
    }
    Write-Host "✅ tcb CLI 已安装" -ForegroundColor Green
} catch {
    Write-Host "❌ tcb CLI 未安装，请先执行:" -ForegroundColor Red
    Write-Host "   npm install -g @cloudbase/cli" -ForegroundColor Yellow
    Write-Host "   tcb login" -ForegroundColor Yellow
    exit 1
}

# 开始批量部署
Write-Host ""
Write-Host "🚀 开始批量部署..." -ForegroundColor Green
Write-Host ""

$success = 0
$failed = 0
$failedList = @()

foreach ($functionName in $functionsToDeploy) {
    Write-Host "部署: $functionName" -ForegroundColor Yellow -NoNewline
    
    try {
        # 执行部署命令
        $output = tcb fn deploy $functionName -e $envId --force 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host " ✅ 成功" -ForegroundColor Green
            $success++
        } else {
            Write-Host " ❌ 失败" -ForegroundColor Red
            $failed++
            $failedList += $functionName
            
            # 显示错误信息（简化）
            if ($output -match "无效身份信息") {
                Write-Host "   错误: 需要登录 (tcb login)" -ForegroundColor Red
            } elseif ($output -match "network|ECONNRESET") {
                Write-Host "   错误: 网络连接问题" -ForegroundColor Red
            } else {
                Write-Host "   错误: 部署失败" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host " ❌ 异常" -ForegroundColor Red
        $failed++
        $failedList += $functionName
        Write-Host "   异常: $_" -ForegroundColor Red
    }
    
    # 添加延迟
    Start-Sleep -Seconds 2
}

# 显示结果
Write-Host ""
Write-Host "📊 部署结果:" -ForegroundColor Cyan
Write-Host "✅ 成功: $success 个" -ForegroundColor Green
Write-Host "❌ 失败: $failed 个" -ForegroundColor Red

if ($failed -gt 0) {
    Write-Host ""
    Write-Host "❌ 失败的函数:" -ForegroundColor Red
    $failedList | ForEach-Object { Write-Host "   - $_" -ForegroundColor Red }
    
    Write-Host ""
    Write-Host "💡 解决建议:" -ForegroundColor Cyan
    Write-Host "1. 检查登录状态: tcb auth list" -ForegroundColor Yellow
    Write-Host "2. 重新登录: tcb login" -ForegroundColor Yellow
    Write-Host "3. 检查网络连接" -ForegroundColor Yellow
    Write-Host "4. 使用微信开发者工具右键部署" -ForegroundColor Yellow
}

if ($success -gt 0) {
    Write-Host ""
    Write-Host "🎉 部分或全部部署成功！" -ForegroundColor Green
    Write-Host ""
    Write-Host "下一步:" -ForegroundColor Cyan
    Write-Host "1. 在微信开发者工具中验证云函数状态" -ForegroundColor Yellow
    Write-Host "2. 运行小程序测试功能" -ForegroundColor Yellow
    if ($success -ge 1 -and $functionsToDeploy -contains "initDatabase") {
        Write-Host "3. 执行数据库初始化" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "完成！" -ForegroundColor Green
