// pages/my-downloads/my-downloads.js
const { materialAPI, userAPI } = require('../../utils/api.js')
const { showError, showSuccess, showConfirm, showLoading, hideLoading } = require('../../utils/util.js')

Page({
  data: {
    // 下载列表
    downloads: [],

    // 分页信息
    currentPage: 1,
    hasMore: true,

    // 状态控制
    loading: true,
    loadingMore: false,

    // 筛选和排序
    sortType: 'time', // time, name, points
    sortOptions: [
      { value: 'time', name: '下载时间' },
      { value: 'name', name: '资料名称' },
      { value: 'points', name: '积分消耗' }
    ],
    currentSortName: '下载时间', // 当前排序方式的名称
    showSortMenu: false,

    // 统计信息
    totalDownloads: 0,
    totalPoints: 0,

    // 用户信息
    userInfo: null,
    isLogin: false
  },

  onLoad(options) {
    this.checkUserStatus()
  },

  onShow() {
    this.checkUserStatus()
    if (this.data.isLogin) {
      this.refreshDownloads()
    }
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore && this.data.isLogin) {
      this.loadMoreDownloads()
    }
  },

  onPullDownRefresh() {
    if (this.data.isLogin) {
      this.refreshDownloads().finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  // 检查用户状态
  checkUserStatus() {
    const app = getApp()
    const userInfo = app.getUserInfo()
    const isLogin = app.globalData.isLogin

    this.setData({
      userInfo,
      isLogin
    })

    if (isLogin) {
      this.loadDownloads()
    } else {
      this.setData({ loading: false })
    }
  },

  // 加载下载列表
  async loadDownloads() {
    if (!this.data.isLogin) return

    try {
      this.setData({ loading: true })

      const params = {
        page: 1,
        limit: 20,
        sortType: this.data.sortType
      }

      const result = await materialAPI.getMyDownloads(params)

      this.setData({
        downloads: result.downloads || [],
        currentPage: 1,
        hasMore: result.hasMore || false,
        totalDownloads: result.total || 0,
        totalPoints: result.totalPoints || 0
      })

    } catch (error) {
      console.error('加载下载列表失败:', error)
      showError('加载失败，请稍后重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载更多下载记录
  async loadMoreDownloads() {
    if (this.data.loadingMore || !this.data.hasMore || !this.data.isLogin) return

    try {
      this.setData({ loadingMore: true })

      const nextPage = this.data.currentPage + 1
      const params = {
        page: nextPage,
        limit: 20,
        sortType: this.data.sortType
      }

      const result = await materialAPI.getMyDownloads(params)

      this.setData({
        downloads: [...this.data.downloads, ...(result.downloads || [])],
        currentPage: nextPage,
        hasMore: result.hasMore || false
      })

    } catch (error) {
      console.error('加载更多失败:', error)
      showError('加载失败')
    } finally {
      this.setData({ loadingMore: false })
    }
  },

  // 刷新下载列表
  async refreshDownloads() {
    this.setData({
      currentPage: 1,
      hasMore: true
    })
    await this.loadDownloads()
  },

  // 点击下载项
  onDownloadTap(e) {
    const download = e.currentTarget.dataset.download
    if (!download || !download.material) return

    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${download.material._id}`
    })
  },

  // 重新下载
  async onRedownload(e) {
    const download = e.currentTarget.dataset.download
    if (!download || !download.material) return

    const confirmed = await showConfirm('确定要重新下载这个资料吗？', '重新下载')
    if (!confirmed) return

    try {
      showLoading('下载中...')

      const result = await materialAPI.downloadMaterial(download.material._id)

      if (result.downloadUrl) {
        await this.saveFileToLocal(result.downloadUrl, download.material.title)
      }

      showSuccess('下载成功')

    } catch (error) {
      console.error('重新下载失败:', error)
      showError('下载失败，请稍后重试')
    } finally {
      hideLoading()
    }
  },

  // 保存文件到本地
  async saveFileToLocal(url, title) {
    try {
      const downloadTask = wx.downloadFile({
        url: url,
        success: (res) => {
          if (res.statusCode === 200) {
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                console.log('文件已保存到相册')
              },
              fail: (error) => {
                console.error('保存到相册失败:', error)
                this.openDocument(res.tempFilePath)
              }
            })
          }
        },
        fail: (error) => {
          console.error('下载文件失败:', error)
          throw error
        }
      })

      return downloadTask
    } catch (error) {
      console.error('保存文件失败:', error)
      throw error
    }
  },

  // 打开文档
  openDocument(filePath) {
    wx.openDocument({
      filePath: filePath,
      success: () => {
        console.log('文档打开成功')
      },
      fail: (error) => {
        console.error('打开文档失败:', error)
      }
    })
  },

  // 删除下载记录
  async onDeleteDownload(e) {
    const download = e.currentTarget.dataset.download
    if (!download) return

    const confirmed = await showConfirm('确定要删除这条下载记录吗？\n（不会影响已下载的文件）', '删除记录')
    if (!confirmed) return

    try {
      await materialAPI.deleteDownloadRecord(download._id)

      // 从列表中移除
      const downloads = this.data.downloads.filter(item => item._id !== download._id)
      this.setData({
        downloads,
        totalDownloads: this.data.totalDownloads - 1
      })

      showSuccess('删除成功')

    } catch (error) {
      console.error('删除下载记录失败:', error)
      showError('删除失败，请稍后重试')
    }
  },

  // 显示排序菜单
  onSortTap() {
    this.setData({
      showSortMenu: !this.data.showSortMenu
    })
  },

  // 选择排序方式
  onSortSelect(e) {
    const sortType = e.currentTarget.dataset.sort
    const sortOption = this.data.sortOptions.find(item => item.value === sortType)

    this.setData({
      sortType,
      currentSortName: sortOption ? sortOption.name : '下载时间',
      showSortMenu: false
    })

    // 重新加载数据
    this.refreshDownloads()
  },

  // 关闭排序菜单
  onCloseSortMenu() {
    this.setData({
      showSortMenu: false
    })
  },

  // 清空下载历史
  async onClearHistory() {
    if (this.data.downloads.length === 0) {
      showError('暂无下载记录')
      return
    }

    const confirmed = await showConfirm('确定要清空所有下载记录吗？\n（不会影响已下载的文件）', '清空记录')
    if (!confirmed) return

    try {
      showLoading('清空中...')

      await materialAPI.clearDownloadHistory()

      this.setData({
        downloads: [],
        totalDownloads: 0,
        totalPoints: 0,
        currentPage: 1,
        hasMore: false
      })

      showSuccess('清空成功')

    } catch (error) {
      console.error('清空下载历史失败:', error)
      showError('清空失败，请稍后重试')
    } finally {
      hideLoading()
    }
  },

  // 去登录
  onGoLogin() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  // 去浏览资料
  onGoBrowse() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '我的下载记录',
      path: '/pages/my-downloads/my-downloads',
      imageUrl: '/images/share-cover.svg'
    }
  }
})