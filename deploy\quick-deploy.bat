@echo off
chcp 65001 >nul
echo.
echo 🚀 微信小程序云函数快速部署工具
echo 📅 2025年最新版 - 基于 cloudbase CLI
echo.

REM 设置环境变量
set ENV_ID=cloud1-8gm001v7fd56ff43
set APP_ID=wxdcb01784f343322b

REM 检查 cloudbase CLI
echo 🔍 检查 cloudbase CLI...
cloudbase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ cloudbase CLI 未安装
    echo 💡 请先执行: npm install -g @cloudbase/cli
    pause
    exit /b 1
)
echo ✅ cloudbase CLI 已安装

REM 检查登录状态
echo 🔍 检查登录状态...
cloudbase auth list >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未登录 cloudbase 账号
    echo 💡 请先执行: cloudbase login
    pause
    exit /b 1
)
echo ✅ 已登录 cloudbase 账号

echo.
echo 🌍 目标环境: %ENV_ID%
echo 📦 开始批量部署云函数...
echo.

REM 部署核心功能（优先级1）
echo 🔥 第一批：核心功能
cloudbase functions deploy initDatabase -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ initDatabase 部署失败 & goto :error
echo ✅ initDatabase 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy getCategories -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ getCategories 部署失败 & goto :error
echo ✅ getCategories 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy getRecommendMaterials -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ getRecommendMaterials 部署失败 & goto :error
echo ✅ getRecommendMaterials 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy login -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ login 部署失败 & goto :error
echo ✅ login 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy searchMaterials -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ searchMaterials 部署失败 & goto :error
echo ✅ searchMaterials 部署成功
timeout /t 2 /nobreak >nul

echo.
echo 📱 第二批：基础功能
cloudbase functions deploy getMaterialDetail -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ getMaterialDetail 部署失败 & goto :error
echo ✅ getMaterialDetail 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy downloadMaterial -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ downloadMaterial 部署失败 & goto :error
echo ✅ downloadMaterial 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy manageFavorite -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ manageFavorite 部署失败 & goto :error
echo ✅ manageFavorite 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy getMyDownloads -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ getMyDownloads 部署失败 & goto :error
echo ✅ getMyDownloads 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy getMyFavorites -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ getMyFavorites 部署失败 & goto :error
echo ✅ getMyFavorites 部署成功
timeout /t 2 /nobreak >nul

echo.
echo 🎯 第三批：高级功能
cloudbase functions deploy generateShareCode -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ generateShareCode 部署失败 & goto :error
echo ✅ generateShareCode 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy handleShareInvite -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ handleShareInvite 部署失败 & goto :error
echo ✅ handleShareInvite 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy getShareStats -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ getShareStats 部署失败 & goto :error
echo ✅ getShareStats 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy recordShareClick -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ recordShareClick 部署失败 & goto :error
echo ✅ recordShareClick 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy uploadMaterial -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ uploadMaterial 部署失败 & goto :error
echo ✅ uploadMaterial 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy validateFile -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ validateFile 部署失败 & goto :error
echo ✅ validateFile 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy submitFeedback -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ submitFeedback 部署失败 & goto :error
echo ✅ submitFeedback 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy getAnnouncements -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ getAnnouncements 部署失败 & goto :error
echo ✅ getAnnouncements 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy checkAdminPermission -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ checkAdminPermission 部署失败 & goto :error
echo ✅ checkAdminPermission 部署成功
timeout /t 2 /nobreak >nul

cloudbase functions deploy getAdminStats -e %ENV_ID%
if %errorlevel% neq 0 echo ❌ getAdminStats 部署失败 & goto :error
echo ✅ getAdminStats 部署成功

echo.
echo 🎉 所有云函数部署完成！
echo.
echo 🔍 建议接下来：
echo 1. 运行小程序验证云函数状态
echo 2. 执行数据库初始化：调用 initDatabase 云函数
echo 3. 测试核心功能是否正常工作
echo.
pause
exit /b 0

:error
echo.
echo ❌ 部署过程中出现错误
echo 💡 请检查网络连接和权限设置
echo.
pause
exit /b 1
