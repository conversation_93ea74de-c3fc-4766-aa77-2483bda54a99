# 方案二：修复网络问题后的批量部署脚本 - 2025年8月
# 包含自动网络修复、登录检查和批量部署功能

param(
    [int]$Priority = 0,
    [switch]$DryRun = $false,
    [switch]$FixNetwork = $false,
    [switch]$ForceReinstall = $false,
    [switch]$Help = $false
)

# 配置信息
$envId = "cloud1-8gm001v7fd56ff43"

# 云函数列表
$cloudFunctions = @(
    @{ Name = "initDatabase"; Priority = 1; Description = "数据库初始化" },
    @{ Name = "getCategories"; Priority = 1; Description = "获取分类数据" },
    @{ Name = "getRecommendMaterials"; Priority = 1; Description = "获取推荐资料" },
    @{ Name = "login"; Priority = 1; Description = "用户登录" },
    @{ Name = "searchMaterials"; Priority = 1; Description = "搜索资料" },
    @{ Name = "getMaterialDetail"; Priority = 2; Description = "获取资料详情" },
    @{ Name = "downloadMaterial"; Priority = 2; Description = "下载资料" },
    @{ Name = "manageFavorite"; Priority = 2; Description = "管理收藏" },
    @{ Name = "getMyDownloads"; Priority = 2; Description = "获取我的下载" },
    @{ Name = "getMyFavorites"; Priority = 2; Description = "获取我的收藏" },
    @{ Name = "generateShareCode"; Priority = 3; Description = "生成分享码" },
    @{ Name = "handleShareInvite"; Priority = 3; Description = "处理分享邀请" },
    @{ Name = "getShareStats"; Priority = 3; Description = "获取分享统计" },
    @{ Name = "recordShareClick"; Priority = 3; Description = "记录分享点击" },
    @{ Name = "uploadMaterial"; Priority = 3; Description = "文件上传" },
    @{ Name = "validateFile"; Priority = 3; Description = "文件验证" },
    @{ Name = "submitFeedback"; Priority = 3; Description = "提交反馈" },
    @{ Name = "getAnnouncements"; Priority = 3; Description = "获取公告" },
    @{ Name = "checkAdminPermission"; Priority = 3; Description = "管理员权限检查" },
    @{ Name = "getAdminStats"; Priority = 3; Description = "获取管理统计" }
)

# 显示帮助信息
function Show-Help {
    Write-Host "🚀 方案二：网络修复 + 批量部署工具 - 2025年8月" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\batch-deploy-fixed.ps1                     # 检查环境并部署所有云函数"
    Write-Host "  .\batch-deploy-fixed.ps1 -Priority 1         # 仅部署优先级1的云函数"
    Write-Host "  .\batch-deploy-fixed.ps1 -FixNetwork         # 修复网络问题后部署"
    Write-Host "  .\batch-deploy-fixed.ps1 -ForceReinstall     # 强制重新安装CLI工具"
    Write-Host "  .\batch-deploy-fixed.ps1 -DryRun             # 预演模式"
    Write-Host ""
    Write-Host "参数说明:" -ForegroundColor Yellow
    Write-Host "  -Priority <1|2|3>   指定部署优先级"
    Write-Host "  -FixNetwork         自动修复网络配置"
    Write-Host "  -ForceReinstall     强制重新安装CLI工具"
    Write-Host "  -DryRun             预演模式，不实际部署"
    Write-Host "  -Help               显示帮助信息"
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  # 完整的修复和部署流程"
    Write-Host "  .\batch-deploy-fixed.ps1 -FixNetwork -Priority 1"
    Write-Host ""
    Write-Host "  # 如果遇到网络问题，先修复再部署"
    Write-Host "  .\batch-deploy-fixed.ps1 -ForceReinstall -FixNetwork"
}

# 修复网络配置
function Fix-NetworkConfiguration {
    Write-Host "🔧 修复网络配置..." -ForegroundColor Cyan
    
    try {
        # 设置npm镜像为国内源
        Write-Host "设置npm镜像为国内源..." -ForegroundColor Yellow
        npm config set registry https://registry.npmmirror.com
        
        # 清除可能的代理设置
        Write-Host "清除代理设置..." -ForegroundColor Yellow
        npm config delete proxy 2>$null
        npm config delete https-proxy 2>$null
        
        # 设置超时时间
        npm config set timeout 60000
        
        Write-Host "✅ 网络配置修复完成" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ 网络配置修复失败: $_" -ForegroundColor Red
        return $false
    }
}

# 重新安装CLI工具
function Reinstall-CLI {
    Write-Host "🔄 重新安装CloudBase CLI..." -ForegroundColor Cyan
    
    try {
        # 卸载旧版本
        Write-Host "卸载旧版本..." -ForegroundColor Yellow
        npm uninstall -g @cloudbase/cli 2>$null
        
        # 清理缓存
        Write-Host "清理npm缓存..." -ForegroundColor Yellow
        npm cache clean --force 2>$null
        
        # 安装新版本
        Write-Host "安装最新版本..." -ForegroundColor Yellow
        $installResult = npm install -g @cloudbase/cli 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ CLI工具安装成功" -ForegroundColor Green
            
            # 验证安装
            $version = tcb --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ CLI工具验证成功: $version" -ForegroundColor Green
                return $true
            }
        }
        
        throw "安装验证失败"
    }
    catch {
        Write-Host "❌ CLI工具安装失败: $_" -ForegroundColor Red
        Write-Host "安装输出: $installResult" -ForegroundColor Red
        return $false
    }
}

# 检查并修复登录状态
function Check-And-Fix-Login {
    Write-Host "🔐 检查登录状态..." -ForegroundColor Cyan
    
    try {
        # 检查当前登录状态
        $authResult = tcb auth list 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 已登录CloudBase账号" -ForegroundColor Green
            return $true
        }
        
        # 如果未登录，尝试登录
        Write-Host "⚠️ 未登录，开始登录流程..." -ForegroundColor Yellow
        Write-Host "请在浏览器中完成登录，然后返回此窗口..." -ForegroundColor Cyan
        
        $loginResult = tcb login 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 登录成功" -ForegroundColor Green
            return $true
        } else {
            throw "登录失败: $loginResult"
        }
    }
    catch {
        Write-Host "❌ 登录检查失败: $_" -ForegroundColor Red
        Write-Host ""
        Write-Host "💡 手动登录步骤:" -ForegroundColor Cyan
        Write-Host "1. 打开新的PowerShell窗口" -ForegroundColor Yellow
        Write-Host "2. 执行: tcb login" -ForegroundColor Yellow
        Write-Host "3. 在浏览器中完成登录" -ForegroundColor Yellow
        Write-Host "4. 返回此窗口重新运行脚本" -ForegroundColor Yellow
        return $false
    }
}

# 检查环境
function Test-Environment {
    Write-Host "🔍 检查部署环境..." -ForegroundColor Cyan
    
    # 检查Node.js
    try {
        $nodeVersion = node --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
        } else {
            throw "Node.js未安装"
        }
    }
    catch {
        Write-Host "❌ Node.js未安装，请先安装Node.js" -ForegroundColor Red
        return $false
    }
    
    # 检查npm
    try {
        $npmVersion = npm --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
        } else {
            throw "npm不可用"
        }
    }
    catch {
        Write-Host "❌ npm不可用" -ForegroundColor Red
        return $false
    }
    
    # 检查tcb CLI
    try {
        $tcbVersion = tcb --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ tcb CLI: $tcbVersion" -ForegroundColor Green
        } else {
            if ($ForceReinstall -or $FixNetwork) {
                Write-Host "⚠️ tcb CLI未安装，将自动安装..." -ForegroundColor Yellow
                return "NEED_INSTALL"
            } else {
                throw "tcb CLI未安装"
            }
        }
    }
    catch {
        Write-Host "❌ tcb CLI未安装，请添加 -ForceReinstall 参数自动安装" -ForegroundColor Red
        return $false
    }
    
    # 检查云函数目录
    if (Test-Path "cloudfunctions") {
        $funcCount = (Get-ChildItem "cloudfunctions" -Directory).Count
        Write-Host "✅ cloudfunctions目录存在，包含 $funcCount 个函数" -ForegroundColor Green
    } else {
        Write-Host "❌ 未找到cloudfunctions目录" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# 部署单个云函数
function Deploy-Function {
    param($FunctionInfo)
    
    $name = $FunctionInfo.Name
    $description = $FunctionInfo.Description
    
    Write-Host "🚀 部署: $name ($description)" -ForegroundColor Yellow
    
    try {
        # 使用正确的tcb命令格式
        $result = tcb fn deploy $name -e $envId --force 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $name 部署成功" -ForegroundColor Green
            return @{ Success = $true; Name = $name; Result = $result }
        } else {
            # 检查是否是网络错误
            if ($result -match "network|ECONNRESET|ETIMEDOUT") {
                Write-Host "⚠️ $name 网络错误，重试中..." -ForegroundColor Yellow
                Start-Sleep -Seconds 5
                
                # 重试一次
                $retryResult = tcb fn deploy $name -e $envId --force 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ $name 重试成功" -ForegroundColor Green
                    return @{ Success = $true; Name = $name; Result = $retryResult }
                }
            }
            
            throw "部署失败: $result"
        }
    }
    catch {
        Write-Host "❌ $name 部署失败: $_" -ForegroundColor Red
        return @{ Success = $false; Name = $name; Error = $_.ToString() }
    }
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Host "🚀 方案二：网络修复 + 批量部署工具" -ForegroundColor Cyan
    Write-Host "📅 2025年8月最新版" -ForegroundColor Cyan
    Write-Host "🌍 目标环境: $envId" -ForegroundColor Cyan
    Write-Host ""
    
    # 修复网络配置
    if ($FixNetwork) {
        if (-not (Fix-NetworkConfiguration)) {
            Write-Host "❌ 网络配置修复失败，退出" -ForegroundColor Red
            exit 1
        }
    }
    
    # 检查环境
    $envCheck = Test-Environment
    if ($envCheck -eq $false) {
        Write-Host "❌ 环境检查失败，退出" -ForegroundColor Red
        exit 1
    }
    
    # 重新安装CLI（如果需要）
    if ($ForceReinstall -or $envCheck -eq "NEED_INSTALL") {
        if (-not (Reinstall-CLI)) {
            Write-Host "❌ CLI工具安装失败，退出" -ForegroundColor Red
            exit 1
        }
    }
    
    # 检查登录状态
    if (-not (Check-And-Fix-Login)) {
        Write-Host "❌ 登录检查失败，请手动登录后重试" -ForegroundColor Red
        exit 1
    }
    
    # 过滤要部署的函数
    $functionsToDeploy = $cloudFunctions
    if ($Priority -gt 0) {
        $functionsToDeploy = $cloudFunctions | Where-Object { $_.Priority -eq $Priority }
        Write-Host "🎯 仅部署优先级 $Priority 的云函数" -ForegroundColor Magenta
    }
    
    Write-Host "📋 准备部署 $($functionsToDeploy.Count) 个云函数..." -ForegroundColor Cyan
    Write-Host ""
    
    # 预演模式
    if ($DryRun) {
        Write-Host "🔍 预演模式 - 不会实际部署" -ForegroundColor Magenta
        Write-Host ""
        foreach ($func in $functionsToDeploy) {
            Write-Host "  - $($func.Name) ($($func.Description))" -ForegroundColor Yellow
        }
        Write-Host ""
        Write-Host "💡 要实际部署，请移除 -DryRun 参数" -ForegroundColor Cyan
        return
    }
    
    # 开始部署
    $results = @{
        Total = $functionsToDeploy.Count
        Success = 0
        Failed = 0
        Details = @()
    }
    
    foreach ($func in $functionsToDeploy) {
        $result = Deploy-Function $func
        $results.Details += $result
        
        if ($result.Success) {
            $results.Success++
        } else {
            $results.Failed++
        }
        
        # 添加延迟避免请求过快
        Start-Sleep -Seconds 3
    }
    
    # 显示结果
    Write-Host ""
    Write-Host "📊 部署结果统计:" -ForegroundColor Cyan
    Write-Host "📦 总计: $($results.Total) 个" -ForegroundColor Blue
    Write-Host "✅ 成功: $($results.Success) 个" -ForegroundColor Green
    Write-Host "❌ 失败: $($results.Failed) 个" -ForegroundColor Red
    
    if ($results.Failed -gt 0) {
        Write-Host ""
        Write-Host "❌ 失败的云函数:" -ForegroundColor Red
        $results.Details | Where-Object { -not $_.Success } | ForEach-Object {
            Write-Host "  - $($_.Name): $($_.Error)" -ForegroundColor Red
        }
        Write-Host ""
        Write-Host "💡 对于失败的函数，建议使用微信开发者工具右键部署" -ForegroundColor Cyan
    }
    
    if ($results.Success -gt 0) {
        Write-Host ""
        Write-Host "✅ 成功的云函数:" -ForegroundColor Green
        $results.Details | Where-Object { $_.Success } | ForEach-Object {
            Write-Host "  - $($_.Name)" -ForegroundColor Green
        }
        
        Write-Host ""
        Write-Host "🎉 部署完成！下一步:" -ForegroundColor Green
        Write-Host "1. 运行小程序验证云函数状态" -ForegroundColor Yellow
        Write-Host "2. 执行数据库初始化" -ForegroundColor Yellow
        Write-Host "3. 测试核心功能" -ForegroundColor Yellow
    }
}

# 执行主函数
Main
