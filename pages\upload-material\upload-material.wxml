<!--pages/upload-material/upload-material.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 表单内容 -->
  <view wx:else class="form-container">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <view class="form-item">
        <text class="form-label">资料标题 *</text>
        <input class="form-input" 
               placeholder="请输入资料标题" 
               value="{{formData.title}}"
               data-field="title"
               bindinput="onInputChange" />
      </view>

      <view class="form-item">
        <text class="form-label">资料描述 *</text>
        <textarea class="form-textarea" 
                  placeholder="请详细描述资料内容、适用范围等"
                  value="{{formData.description}}"
                  data-field="description"
                  bindinput="onInputChange"
                  maxlength="500"></textarea>
      </view>

      <view class="form-item">
        <text class="form-label">标签</text>
        <input class="form-input" 
               placeholder="请输入标签，用逗号分隔" 
               value="{{formData.tags}}"
               data-field="tags"
               bindinput="onInputChange" />
      </view>
    </view>

    <!-- 分类信息 -->
    <view class="form-section">
      <view class="section-title">分类信息</view>
      
      <view class="form-item">
        <text class="form-label">年级 *</text>
        <picker class="form-picker"
                mode="selector"
                range="{{gradeOptions}}"
                range-key="name"
                data-field="grade"
                bindchange="onPickerChange">
          <view class="picker-text">
            {{formData.grade ? gradeName : '请选择年级'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">科目 *</text>
        <picker class="form-picker"
                mode="selector"
                range="{{subjectOptions}}"
                range-key="name"
                data-field="subject"
                bindchange="onPickerChange">
          <view class="picker-text">
            {{formData.subject ? subjectName : '请选择科目'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">上下册</text>
        <picker class="form-picker"
                mode="selector"
                range="{{semesterOptions}}"
                range-key="name"
                data-field="semester"
                bindchange="onPickerChange">
          <view class="picker-text">
            {{formData.semester ? semesterName : '请选择上下册'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">教材版本</text>
        <picker class="form-picker"
                mode="selector"
                range="{{textbookOptions}}"
                range-key="name"
                data-field="textbook"
                bindchange="onPickerChange">
          <view class="picker-text">
            {{formData.textbook ? textbookName : '请选择教材版本'}}
          </view>
        </picker>
      </view>
    </view>

    <!-- 积分设置 -->
    <view class="form-section">
      <view class="section-title">积分设置</view>
      
      <view class="form-item">
        <text class="form-label">下载积分 *</text>
        <input class="form-input" 
               type="number"
               placeholder="1-1000" 
               value="{{formData.points}}"
               data-field="points"
               bindinput="onInputChange" />
        <text class="form-tip">用户下载此资料需要消耗的积分</text>
      </view>
    </view>

    <!-- 文件上传 -->
    <view class="form-section">
      <view class="section-title">文件上传</view>
      
      <!-- 未选择文件 -->
      <view wx:if="{{!selectedFile}}" class="file-upload-area" bindtap="onChooseFile">
        <image src="/images/icon-upload.svg" class="upload-icon" mode="aspectFit"></image>
        <text class="upload-text">点击选择文件</text>
        <text class="upload-tip">支持PDF、Word、PPT、Excel格式，最大50MB</text>
      </view>

      <!-- 已选择文件 -->
      <view wx:else class="file-info">
        <view class="file-header">
          <text class="file-name">{{selectedFile.name}}</text>
          <button class="remove-btn" bindtap="onRemoveFile">移除</button>
        </view>
        
        <view class="file-details">
          <text class="file-size">大小：{{formatFileSize(selectedFile.size)}}</text>
          <text wx:if="{{fileValidation}}" class="file-type">类型：{{fileValidation.metadata.typeName}}</text>
        </view>

        <!-- 验证结果 -->
        <view wx:if="{{fileValidation && fileValidation.recommendations}}" class="file-recommendations">
          <view wx:for="{{fileValidation.recommendations}}" wx:key="index" 
                class="recommendation {{item.type}}">
            <text>{{item.message}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" 
              bindtap="onSubmit" 
              disabled="{{uploading || !selectedFile}}"
              loading="{{uploading}}">
        {{uploading ? '上传中...' : '提交资料'}}
      </button>
      
      <view class="submit-tip">
        <text>提交后需要管理员审核通过才能发布</text>
      </view>
    </view>
  </view>
</view>
