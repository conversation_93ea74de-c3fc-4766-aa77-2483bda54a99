# 🚀 快速部署指南

## 云环境配置确认
✅ **云环境ID**: `cloud1-8gm001v7fd56ff43` (已配置)

## 一键部署步骤

### 1. 📁 云函数批量部署

在微信开发者工具中：

1. **打开云开发控制台**
   - 点击工具栏的"云开发"按钮
   - 选择环境 `cloud1-8gm001v7fd56ff43`

2. **批量上传云函数**
   - 右键点击项目中的 `cloudfunctions` 文件夹
   - 选择 "上传并部署：云端安装依赖"
   - 等待所有云函数部署完成

3. **验证部署状态**
   - 在云开发控制台的"云函数"页面查看
   - 确认所有函数状态为"部署成功"

### 2. 🗄️ 数据库快速初始化

#### 方法一：控制台创建（推荐）
在云开发控制台的"数据库"页面：

```javascript
// 1. 创建集合（按顺序创建）
collections = [
  'users',           // 用户信息
  'categories',      // 分类信息  
  'materials',       // 资料信息
  'config',          // 系统配置
  'user_downloads',  // 下载记录
  'user_favorites',  // 收藏记录
  'points_log',      // 积分记录
  'share_records',   // 分享记录
  'share_click_log', // 分享点击日志
  'upload_log',      // 上传日志
  'feedback',        // 用户反馈
  'announcements',   // 公告
  'user_announcement_log' // 公告阅读日志
]

// 2. 导入初始数据
// 将 database/init-data.js 中的数据导入到对应集合
```

#### 方法二：云函数初始化
创建一个临时的初始化云函数：

```javascript
// cloudfunctions/initDatabase/index.js
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  try {
    // 导入分类数据
    await db.collection('categories').add({
      data: {
        _id: 'grade',
        name: '年级',
        options: [
          { id: 'grade-1', name: '一年级', is_active: true, sort_order: 1 },
          { id: 'grade-2', name: '二年级', is_active: true, sort_order: 2 },
          // ... 更多数据
        ]
      }
    })
    
    return { success: true, message: '数据库初始化完成' }
  } catch (error) {
    return { success: false, error: error.message }
  }
}
```

### 3. 🔧 权限配置

在云开发控制台设置数据库权限：

```json
{
  "read": true,
  "write": "auth.openid == resource.user_openid || auth.openid == 'admin-openid'"
}
```

### 4. 📱 小程序测试

1. **编译运行**
   - 点击"编译"按钮
   - 检查控制台是否有错误

2. **功能验证**
   - 测试首页加载
   - 测试分类浏览
   - 测试搜索功能
   - 测试用户登录

## 常用云函数代码模板

### 基础云函数模板
```javascript
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 业务逻辑
    const result = await db.collection('collection_name').get()
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    console.error('云函数执行失败:', error)
    return {
      success: false,
      message: error.message
    }
  }
}
```

### package.json 模板
```json
{
  "name": "function-name",
  "version": "1.0.0",
  "description": "",
  "main": "index.js",
  "dependencies": {
    "wx-server-sdk": "~2.6.3"
  }
}
```

## 部署验证清单

### ✅ 云函数验证
```javascript
// 在小程序中测试云函数调用
wx.cloud.callFunction({
  name: 'getCategories',
  success: res => {
    console.log('云函数调用成功:', res)
  },
  fail: err => {
    console.error('云函数调用失败:', err)
  }
})
```

### ✅ 数据库验证
```javascript
// 测试数据库读取
const db = wx.cloud.database()
db.collection('categories').get().then(res => {
  console.log('数据库读取成功:', res.data)
})
```

### ✅ 存储验证
```javascript
// 测试文件上传
wx.cloud.uploadFile({
  cloudPath: 'test.txt',
  filePath: 'temp://test.txt',
  success: res => {
    console.log('文件上传成功:', res.fileID)
  }
})
```

## 故障排除

### 🔧 常见问题

1. **云函数调用失败**
   ```
   解决方案：
   - 检查函数名称是否正确
   - 确认函数已成功部署
   - 查看云函数日志
   ```

2. **数据库权限错误**
   ```
   解决方案：
   - 检查数据库权限设置
   - 确认用户已登录
   - 验证openid是否正确
   ```

3. **环境配置错误**
   ```
   解决方案：
   - 确认环境ID正确
   - 检查云开发是否已开通
   - 验证配额是否充足
   ```

## 性能优化建议

### 📈 云函数优化
- 合理设置内存配置
- 避免冷启动影响
- 优化代码执行效率
- 合理使用缓存

### 📊 数据库优化
- 创建合适的索引
- 优化查询语句
- 控制返回数据量
- 使用分页查询

### 💾 存储优化
- 压缩图片文件
- 设置合理的缓存策略
- 使用CDN加速
- 定期清理无用文件

## 监控和维护

### 📊 监控指标
- 云函数调用次数和耗时
- 数据库读写次数
- 存储空间使用量
- 错误率和成功率

### 🔧 维护任务
- 定期备份重要数据
- 更新云函数代码
- 清理过期数据
- 监控资源使用情况

---

**部署完成后，您的小程序就可以正常使用所有功能了！** 🎉
