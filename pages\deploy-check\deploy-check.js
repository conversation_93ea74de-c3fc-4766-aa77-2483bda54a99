// pages/deploy-check/deploy-check.js
const { getConfig } = require('../../config/env.js')

Page({
  data: {
    envConfig: null,
    cloudFunctions: [
      { name: 'getCategories', status: 'checking', description: '获取分类数据' },
      { name: 'getRecommendMaterials', status: 'checking', description: '获取推荐资料' },
      { name: 'searchMaterials', status: 'checking', description: '搜索资料' },
      { name: 'getMaterialDetail', status: 'checking', description: '获取资料详情' },
      { name: 'login', status: 'checking', description: '用户登录' },
      { name: 'getUserInfo', status: 'checking', description: '获取用户信息' },
      { name: 'downloadMaterial', status: 'checking', description: '下载资料' },
      { name: 'manageFavorite', status: 'checking', description: '管理收藏' },
      { name: 'earnPointsByAd', status: 'checking', description: '观看广告获取积分' },
      { name: 'getPointsLog', status: 'checking', description: '获取积分记录' }
    ],
    databases: [
      { name: 'categories', status: 'checking', description: '分类数据表' },
      { name: 'materials', status: 'checking', description: '资料数据表' },
      { name: 'users', status: 'checking', description: '用户数据表' },
      { name: 'config', status: 'checking', description: '系统配置表' }
    ],
    checking: false,
    allPassed: false
  },

  onLoad() {
    this.initEnvConfig()
  },

  // 初始化环境配置
  initEnvConfig() {
    const envConfig = getConfig()
    this.setData({
      envConfig
    })
  },

  // 开始检查
  async onStartCheck() {
    this.setData({ 
      checking: true,
      allPassed: false
    })

    // 检查云函数
    await this.checkCloudFunctions()
    
    // 检查数据库
    await this.checkDatabases()

    // 检查是否全部通过
    const allFunctionsPassed = this.data.cloudFunctions.every(fn => fn.status === 'success')
    const allDatabasesPassed = this.data.databases.every(db => db.status === 'success')
    
    this.setData({ 
      checking: false,
      allPassed: allFunctionsPassed && allDatabasesPassed
    })

    if (this.data.allPassed) {
      wx.showToast({
        title: '所有检查通过！',
        icon: 'success'
      })
    } else {
      wx.showToast({
        title: '部分检查未通过',
        icon: 'none'
      })
    }
  },

  // 检查云函数
  async checkCloudFunctions() {
    const functions = this.data.cloudFunctions
    
    for (let i = 0; i < functions.length; i++) {
      const func = functions[i]
      
      try {
        // 更新状态为检查中
        func.status = 'checking'
        this.setData({ cloudFunctions: functions })
        
        // 调用云函数测试
        const result = await wx.cloud.callFunction({
          name: func.name,
          data: this.getTestData(func.name)
        })
        
        // 检查返回结果
        if (result && (result.result || result.errMsg === 'cloud.callFunction:ok')) {
          func.status = 'success'
          func.message = '调用成功'
        } else {
          func.status = 'failed'
          func.message = '返回结果异常'
        }
        
      } catch (error) {
        func.status = 'failed'
        func.message = error.message || '调用失败'
      }
      
      // 更新状态
      this.setData({ cloudFunctions: functions })
      
      // 添加延迟避免请求过快
      await this.delay(500)
    }
  },

  // 检查数据库
  async checkDatabases() {
    const databases = this.data.databases
    const db = wx.cloud.database()
    
    for (let i = 0; i < databases.length; i++) {
      const database = databases[i]
      
      try {
        // 更新状态为检查中
        database.status = 'checking'
        this.setData({ databases })
        
        // 尝试读取集合
        const result = await db.collection(database.name).limit(1).get()
        
        if (result) {
          database.status = 'success'
          database.message = `集合存在，包含 ${result.data.length} 条记录`
        } else {
          database.status = 'failed'
          database.message = '集合不存在或无权限访问'
        }
        
      } catch (error) {
        database.status = 'failed'
        database.message = error.message || '访问失败'
      }
      
      // 更新状态
      this.setData({ databases })
      
      // 添加延迟
      await this.delay(300)
    }
  },

  // 获取测试数据
  getTestData(functionName) {
    const testData = {
      'getCategories': {},
      'getRecommendMaterials': { page: 1, limit: 5 },
      'searchMaterials': { keyword: '测试', page: 1, limit: 5 },
      'getMaterialDetail': { materialId: 'test-id' },
      'login': { userInfo: { nickName: '测试用户' } },
      'getUserInfo': {},
      'downloadMaterial': { materialId: 'test-id' },
      'manageFavorite': { materialId: 'test-id', action: 'add' },
      'earnPointsByAd': { adType: 'video' },
      'getPointsLog': { page: 1, limit: 10 }
    }
    
    return testData[functionName] || {}
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // 重新检查单个云函数
  async onRetryFunction(e) {
    const { index } = e.currentTarget.dataset
    const functions = this.data.cloudFunctions
    const func = functions[index]
    
    try {
      func.status = 'checking'
      this.setData({ cloudFunctions: functions })
      
      const result = await wx.cloud.callFunction({
        name: func.name,
        data: this.getTestData(func.name)
      })
      
      if (result && (result.result || result.errMsg === 'cloud.callFunction:ok')) {
        func.status = 'success'
        func.message = '调用成功'
      } else {
        func.status = 'failed'
        func.message = '返回结果异常'
      }
      
    } catch (error) {
      func.status = 'failed'
      func.message = error.message || '调用失败'
    }
    
    this.setData({ cloudFunctions: functions })
  },

  // 重新检查单个数据库
  async onRetryDatabase(e) {
    const { index } = e.currentTarget.dataset
    const databases = this.data.databases
    const database = databases[index]
    const db = wx.cloud.database()
    
    try {
      database.status = 'checking'
      this.setData({ databases })
      
      const result = await db.collection(database.name).limit(1).get()
      
      if (result) {
        database.status = 'success'
        database.message = `集合存在，包含 ${result.data.length} 条记录`
      } else {
        database.status = 'failed'
        database.message = '集合不存在或无权限访问'
      }
      
    } catch (error) {
      database.status = 'failed'
      database.message = error.message || '访问失败'
    }
    
    this.setData({ databases })
  },

  // 查看部署指南
  onViewGuide() {
    wx.showModal({
      title: '部署指南',
      content: '请查看项目中的 deploy/quick-deploy-guide.md 文件获取详细的部署指南',
      showCancel: false
    })
  },

  // 获取状态图标
  getStatusIcon(status) {
    switch (status) {
      case 'success':
        return '✅'
      case 'failed':
        return '❌'
      case 'checking':
        return '🔄'
      default:
        return '⏳'
    }
  }
})
