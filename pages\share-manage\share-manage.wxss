/* pages/share-manage/share-manage.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 未登录状态 */
.login-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.login-btn {
  background-color: #FF6B35;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  border: none;
}

/* 内容区域 */
.content {
  padding: 20rpx;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  color: #999;
}

/* 卡片样式 */
.share-card,
.stats-card,
.records-card {
  background-color: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.refresh-btn {
  background-color: #f0f0f0;
  color: #666;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  border: none;
}

.refresh-btn.loading {
  opacity: 0.6;
}

/* 分享码区域 */
.share-code-section {
  padding: 30rpx;
}

.share-code {
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 30rpx;
  text-align: center;
  font-size: 48rpx;
  font-weight: 600;
  color: #FF6B35;
  letter-spacing: 4rpx;
  margin-bottom: 30rpx;
  font-family: 'Courier New', monospace;
}

.share-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: none;
}

.action-btn.primary {
  background-color: #FF6B35;
  color: white;
}

.action-btn[disabled] {
  opacity: 0.5;
}

.share-tip {
  padding: 20rpx 30rpx;
  background-color: #fff7f0;
  border-top: 1rpx solid #f0f0f0;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rpx;
  background-color: #f0f0f0;
}

.stat-item {
  background-color: white;
  padding: 40rpx 20rpx;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #FF6B35;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 记录列表 */
.records-list {
  padding: 0 30rpx 20rpx;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-info {
  flex: 1;
}

.record-code {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-stats {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.record-stat {
  font-size: 24rpx;
  color: #666;
}

.record-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
}

.record-status.active {
  background-color: #e8f5e8;
  color: #52c41a;
}

.record-status.inactive {
  background-color: #f0f0f0;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}

/* 操作按钮 */
.action-buttons {
  padding: 40rpx 20rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

.share-btn {
  width: 100%;
  background-color: #FF6B35;
  color: white;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.share-btn[disabled] {
  background-color: #ccc;
}

.btn-icon {
  width: 40rpx;
  height: 40rpx;
}
