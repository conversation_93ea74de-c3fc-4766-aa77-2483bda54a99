// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    // 检查管理员权限
    const adminCheck = await checkAdminPermission(openid)
    if (!adminCheck.isAdmin) {
      return {
        success: false,
        message: '权限不足'
      }
    }

    // 并行获取统计数据
    const [
      totalUsers,
      totalMaterials,
      totalDownloads,
      totalPoints,
      pendingMaterials,
      activeUsers
    ] = await Promise.all([
      getUserCount(),
      getMaterialCount(),
      getDownloadCount(),
      getTotalPoints(),
      getPendingMaterialCount(),
      getActiveUserCount()
    ])

    const stats = {
      totalUsers,
      totalMaterials,
      totalDownloads,
      totalPoints,
      pendingMaterials,
      activeUsers,
      updateTime: new Date()
    }

    return {
      success: true,
      data: stats
    }

  } catch (error) {
    console.error('获取管理统计失败:', error)
    return {
      success: false,
      message: '获取统计数据失败'
    }
  }
}

// 检查管理员权限
async function checkAdminPermission(openid) {
  const ADMIN_OPENIDS = [
    'admin-openid-1',
    'admin-openid-2'
  ]
  
  return {
    isAdmin: ADMIN_OPENIDS.includes(openid)
  }
}

// 获取用户总数
async function getUserCount() {
  try {
    const result = await db.collection('users').count()
    return result.total || 0
  } catch (error) {
    console.error('获取用户总数失败:', error)
    return 0
  }
}

// 获取资料总数
async function getMaterialCount() {
  try {
    const result = await db.collection('materials')
      .where({
        is_active: true
      })
      .count()
    return result.total || 0
  } catch (error) {
    console.error('获取资料总数失败:', error)
    return 0
  }
}

// 获取下载总数
async function getDownloadCount() {
  try {
    const result = await db.collection('user_downloads').count()
    return result.total || 0
  } catch (error) {
    console.error('获取下载总数失败:', error)
    return 0
  }
}

// 获取积分总数
async function getTotalPoints() {
  try {
    const result = await db.collection('users')
      .field({
        points: true
      })
      .get()
    
    const totalPoints = result.data.reduce((sum, user) => sum + (user.points || 0), 0)
    return totalPoints
  } catch (error) {
    console.error('获取积分总数失败:', error)
    return 0
  }
}

// 获取待审核资料数量
async function getPendingMaterialCount() {
  try {
    const result = await db.collection('materials')
      .where({
        status: 'pending'
      })
      .count()
    return result.total || 0
  } catch (error) {
    console.error('获取待审核资料数量失败:', error)
    return 0
  }
}

// 获取活跃用户数量（最近7天有活动的用户）
async function getActiveUserCount() {
  try {
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    
    const result = await db.collection('users')
      .where({
        lastActiveTime: db.command.gte(sevenDaysAgo)
      })
      .count()
    
    return result.total || 0
  } catch (error) {
    console.error('获取活跃用户数量失败:', error)
    return 0
  }
}
