# 🚀 修复后的云函数部署指南 - 2025年8月最新版

## ❌ 问题分析

您遇到的错误：
```
error: unknown option '-e'
```

**根本原因**：您的部署脚本使用了错误的命令格式。

## ✅ 正确的部署方法

### 方法一：使用正确的 tcb 命令 ⭐ 推荐

根据最新的 CloudBase CLI 2.7.8 版本，正确的命令格式是：

```bash
# 正确的命令格式
tcb fn deploy <functionName> -e <envId>

# 具体示例
tcb fn deploy initDatabase -e cloud1-8gm001v7fd56ff43
tcb fn deploy getCategories -e cloud1-8gm001v7fd56ff43
```

### 方法二：使用修复后的批量部署脚本

我已经修复了您的 `batch-deploy.ps1` 脚本，现在可以正常使用：

```powershell
# 部署所有云函数
.\deploy\batch-deploy.ps1

# 仅部署核心功能（优先级1）
.\deploy\batch-deploy.ps1 -Priority 1

# 预演模式（查看将要部署的函数）
.\deploy\batch-deploy.ps1 -DryRun
```

### 方法三：手动逐个部署

如果脚本仍有问题，可以手动执行：

```bash
# 首先确保已登录
tcb login

# 逐个部署云函数（按优先级顺序）
tcb fn deploy initDatabase -e cloud1-8gm001v7fd56ff43
tcb fn deploy getCategories -e cloud1-8gm001v7fd56ff43
tcb fn deploy getRecommendMaterials -e cloud1-8gm001v7fd56ff43
tcb fn deploy login -e cloud1-8gm001v7fd56ff43
tcb fn deploy searchMaterials -e cloud1-8gm001v7fd56ff43

# 继续部署其他函数...
tcb fn deploy getMaterialDetail -e cloud1-8gm001v7fd56ff43
tcb fn deploy downloadMaterial -e cloud1-8gm001v7fd56ff43
tcb fn deploy manageFavorite -e cloud1-8gm001v7fd56ff43
tcb fn deploy getMyDownloads -e cloud1-8gm001v7fd56ff43
tcb fn deploy getMyFavorites -e cloud1-8gm001v7fd56ff43

# 高级功能
tcb fn deploy generateShareCode -e cloud1-8gm001v7fd56ff43
tcb fn deploy handleShareInvite -e cloud1-8gm001v7fd56ff43
tcb fn deploy getShareStats -e cloud1-8gm001v7fd56ff43
tcb fn deploy recordShareClick -e cloud1-8gm001v7fd56ff43
tcb fn deploy uploadMaterial -e cloud1-8gm001v7fd56ff43
tcb fn deploy validateFile -e cloud1-8gm001v7fd56ff43
tcb fn deploy submitFeedback -e cloud1-8gm001v7fd56ff43
tcb fn deploy getAnnouncements -e cloud1-8gm001v7fd56ff43
tcb fn deploy checkAdminPermission -e cloud1-8gm001v7fd56ff43
tcb fn deploy getAdminStats -e cloud1-8gm001v7fd56ff43
```

## 🔧 立即执行步骤

### 步骤1：验证 CLI 工具

```bash
# 检查版本
tcb --version

# 检查登录状态
tcb auth list

# 如果未登录，执行登录
tcb login
```

### 步骤2：开始部署

**选择以下任一方式：**

#### 方式A：使用修复后的脚本（推荐）
```powershell
cd d:\code\wx_k12
.\deploy\batch-deploy.ps1 -Priority 1
```

#### 方式B：手动部署核心功能
```bash
cd d:\code\wx_k12
tcb fn deploy initDatabase -e cloud1-8gm001v7fd56ff43
tcb fn deploy getCategories -e cloud1-8gm001v7fd56ff43
tcb fn deploy getRecommendMaterials -e cloud1-8gm001v7fd56ff43
tcb fn deploy login -e cloud1-8gm001v7fd56ff43
tcb fn deploy searchMaterials -e cloud1-8gm001v7fd56ff43
```

### 步骤3：验证部署结果

部署完成后，检查：

1. **云开发控制台**
   - 打开微信开发者工具
   - 点击"云开发"按钮
   - 查看"云函数"列表
   - 确认函数状态为"部署成功"

2. **测试云函数**
   - 在控制台点击函数名
   - 点击"测试"按钮
   - 查看运行结果

## 🆘 如果仍有问题

### 问题1：tcb 命令不存在
```bash
# 重新安装 CLI
npm uninstall -g @cloudbase/cli
npm install -g @cloudbase/cli

# 验证安装
tcb --version
```

### 问题2：权限问题
```bash
# 重新登录
tcb logout
tcb login
```

### 问题3：网络问题
```bash
# 使用国内镜像
npm config set registry https://registry.npmmirror.com
npm install -g @cloudbase/cli
```

## 🎯 部署优先级建议

**第一批（必须）**：
1. initDatabase - 数据库初始化
2. getCategories - 分类数据
3. login - 用户登录

**第二批（重要）**：
4. getRecommendMaterials - 推荐资料
5. searchMaterials - 搜索功能
6. getMaterialDetail - 资料详情

**第三批（其他）**：
7-20. 其他业务功能

## 🚀 现在开始

**立即执行以下命令开始部署：**

```bash
# 进入项目目录
cd d:\code\wx_k12

# 检查环境
tcb --version
tcb auth list

# 开始部署第一个最重要的函数
tcb fn deploy initDatabase -e cloud1-8gm001v7fd56ff43
```

部署成功后，您会看到类似这样的输出：
```
✅ [initDatabase] 部署成功
```

然后继续部署其他函数。
