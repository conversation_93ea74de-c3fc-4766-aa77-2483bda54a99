# 🚀 2025年最新批量云函数部署方案

## 📋 基于最新微信云开发的批量部署方法

**云环境ID**: `cloud1-8gm001v7fd56ff43`  
**云函数数量**: 20个  
**部署方式**: 命令行工具 + 脚本自动化

---

## ⚙️ **方法一：cloudbase/cli 命令行工具（推荐）**

### 1. 安装 cloudbase CLI 工具

```bash
# 全局安装微信云开发CLI
npm install -g @cloudbase/cli

# 验证安装
cloudbase --version
```

### 2. 登录和环境配置

```bash
# 登录云开发账号
cloudbase login

# 设置目标环境
cloudbase config set env cloud1-8gm001v7fd56ff43

# 验证环境配置
cloudbase env list
```

### 3. 批量部署云函数

#### 方式A：增量部署命令（推荐）

```bash
# 进入项目根目录
cd /path/to/wx_k12

# 批量部署所有云函数
cloudbase functions deploy initDatabase -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy getCategories -e cloud1-8gm001v7fd56ff43

# ... 继续部署其他云函数
```

#### 方式B：使用 functions:deploy 命令

```bash
# 逐个部署云函数
cloudbase functions deploy initDatabase -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy getCategories -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy getRecommendMaterials -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy login -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy searchMaterials -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy getMaterialDetail -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy downloadMaterial -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy manageFavorite -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy getMyDownloads -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy getMyFavorites -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy generateShareCode -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy handleShareInvite -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy getShareStats -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy recordShareClick -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy uploadMaterial -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy validateFile -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy submitFeedback -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy getAnnouncements -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy checkAdminPermission -e cloud1-8gm001v7fd56ff43
cloudbase functions deploy getAdminStats -e cloud1-8gm001v7fd56ff43
```

---

## 🔄 **方法二：脚本自动化部署（最高效）**

### 1. Node.js 自动化部署脚本

创建 `deploy/auto-deploy.js`：

```javascript
const { exec } = require('child_process');
const path = require('path');

// 云函数列表（按优先级排序）
const cloudFunctions = [
  // 核心功能
  'initDatabase',
  'getCategories', 
  'getRecommendMaterials',
  'login',
  'searchMaterials',
  
  // 基础功能
  'getMaterialDetail',
  'downloadMaterial',
  'manageFavorite',
  'getMyDownloads',
  'getMyFavorites',
  
  // 高级功能
  'generateShareCode',
  'handleShareInvite',
  'getShareStats',
  'recordShareClick',
  'uploadMaterial',
  'validateFile',
  'submitFeedback',
  'getAnnouncements',
  'checkAdminPermission',
  'getAdminStats'
];

const envId = 'cloud1-8gm001v7fd56ff43';
const appId = 'wxdcb01784f343322b';

// 部署单个云函数
function deployFunction(functionName) {
  return new Promise((resolve, reject) => {
    const command = `cloudbase functions:deploy ${functionName} --env ${envId}`;
    
    console.log(`🚀 正在部署: ${functionName}`);
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ ${functionName} 部署失败:`, error.message);
        reject(error);
      } else {
        console.log(`✅ ${functionName} 部署成功`);
        console.log(stdout);
        resolve(stdout);
      }
    });
  });
}

// 批量部署所有云函数
async function deployAllFunctions() {
  console.log(`📋 开始批量部署 ${cloudFunctions.length} 个云函数...`);
  console.log(`🌍 目标环境: ${envId}`);
  
  let successCount = 0;
  let failedCount = 0;
  const failedFunctions = [];
  
  for (const functionName of cloudFunctions) {
    try {
      await deployFunction(functionName);
      successCount++;
      
      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      failedCount++;
      failedFunctions.push(functionName);
    }
  }
  
  console.log('\n📊 部署结果统计:');
  console.log(`✅ 成功: ${successCount} 个`);
  console.log(`❌ 失败: ${failedCount} 个`);
  
  if (failedFunctions.length > 0) {
    console.log(`\n❌ 失败的云函数:`);
    failedFunctions.forEach(name => console.log(`  - ${name}`));
  }
  
  console.log('\n🎉 批量部署完成!');
}

// 执行部署
deployAllFunctions().catch(console.error);
```

### 2. 执行自动化部署

```bash
# 进入项目目录
cd /path/to/wx_k12

# 执行自动化部署脚本
node deploy/auto-deploy.js
```

---

## 🔧 **方法三：PowerShell 批量部署脚本（Windows）**

### 创建 `deploy/batch-deploy.ps1`：

```powershell
# PowerShell 批量部署脚本
$envId = "cloud1-8gm001v7fd56ff43"
$functions = @(
    "initDatabase",
    "getCategories", 
    "getRecommendMaterials",
    "login",
    "searchMaterials",
    "getMaterialDetail",
    "downloadMaterial",
    "manageFavorite",
    "getMyDownloads",
    "getMyFavorites",
    "generateShareCode",
    "handleShareInvite",
    "getShareStats",
    "recordShareClick",
    "uploadMaterial",
    "validateFile",
    "submitFeedback",
    "getAnnouncements",
    "checkAdminPermission",
    "getAdminStats"
)

Write-Host "🚀 开始批量部署云函数..." -ForegroundColor Green
Write-Host "🌍 目标环境: $envId" -ForegroundColor Cyan

$successCount = 0
$failedCount = 0
$failedFunctions = @()

foreach ($function in $functions) {
    Write-Host "📤 正在部署: $function" -ForegroundColor Yellow
    
    try {
        $result = cloudbase functions:deploy $function --env $envId
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $function 部署成功" -ForegroundColor Green
            $successCount++
        } else {
            throw "部署命令执行失败"
        }
    }
    catch {
        Write-Host "❌ $function 部署失败: $_" -ForegroundColor Red
        $failedCount++
        $failedFunctions += $function
    }
    
    # 添加延迟
    Start-Sleep -Seconds 2
}

Write-Host "`n📊 部署结果统计:" -ForegroundColor Cyan
Write-Host "✅ 成功: $successCount 个" -ForegroundColor Green
Write-Host "❌ 失败: $failedCount 个" -ForegroundColor Red

if ($failedFunctions.Count -gt 0) {
    Write-Host "`n❌ 失败的云函数:" -ForegroundColor Red
    foreach ($failed in $failedFunctions) {
        Write-Host "  - $failed" -ForegroundColor Red
    }
}

Write-Host "`n🎉 批量部署完成!" -ForegroundColor Green
```

### 执行 PowerShell 脚本：

```powershell
# 在 PowerShell 中执行
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\deploy\batch-deploy.ps1
```

---

## ⚠️ **注意事项与优化建议**

### 1. 环境一致性
- ✅ 所有云函数已使用 `cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })`
- ✅ 避免硬编码环境ID

### 2. 权限管理
```bash
# 确保已登录有权限的账号
cloudbase login

# 检查当前登录状态
cloudbase auth list
```

### 3. 依赖管理
```bash
# 为每个云函数安装依赖（如果需要）
cd cloudfunctions/initDatabase && npm install
cd ../getCategories && npm install
# ... 其他云函数
```

### 4. 验证与测试
```bash
# 部署后验证云函数状态
cloudbase functions:list --env cloud1-8gm001v7fd56ff43

# 测试特定云函数
cloudbase functions:invoke initDatabase --env cloud1-8gm001v7fd56ff43
```

---

## 💎 **适用场景对比**

| **方法**          | **适用场景**                           | **效率** | **复杂度** |
|-------------------|---------------------------------------|----------|------------|
| CLI 增量部署      | 修改部分函数的增量更新                 | ⭐⭐⭐⭐     | 中         |
| Node.js 脚本      | 首次部署或大量函数批量部署             | ⭐⭐⭐⭐⭐    | 中         |
| PowerShell 脚本   | Windows 环境下的自动化部署             | ⭐⭐⭐⭐⭐    | 低         |
| 手动控制台部署    | 少量函数或测试环境                     | ⭐⭐       | 低         |

---

## 🎯 **推荐部署流程**

### 第一次部署（推荐使用 Node.js 脚本）：
1. 安装 cloudbase CLI
2. 登录并配置环境
3. 执行 `node deploy/auto-deploy.js`
4. 验证部署结果

### 后续增量更新（推荐使用 CLI 命令）：
1. 修改特定云函数代码
2. 使用 `cloudbase functions:deploy <函数名>` 单独部署
3. 或使用 `inc-deploy` 命令增量更新

---

## 🚀 **立即开始部署**

选择最适合您的方法：

1. **快速开始**：使用 PowerShell 脚本（Windows 用户）
2. **灵活控制**：使用 Node.js 脚本
3. **手动精确**：使用 CLI 命令逐个部署

**现在就开始第一步：安装 cloudbase CLI 工具！**

```bash
npm install -g @cloudbase/cli
cloudbase login
```
