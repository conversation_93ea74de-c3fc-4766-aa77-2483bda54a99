<!--pages/admin/admin.wxml-->
<view class="container">
  <!-- 权限不足 -->
  <view wx:if="{{!isAdmin}}" class="no-permission">
    <image src="/images/empty-materials.svg" class="empty-icon" mode="aspectFit"></image>
    <text class="empty-text">权限验证中...</text>
  </view>

  <!-- 管理后台内容 -->
  <view wx:else class="admin-content">
    <!-- 顶部导航 -->
    <view class="tab-nav">
      <view class="tab-item {{currentTab === 'stats' ? 'active' : ''}}" 
            data-tab="stats" bindtap="onTabChange">
        <text>统计</text>
      </view>
      <view class="tab-item {{currentTab === 'materials' ? 'active' : ''}}" 
            data-tab="materials" bindtap="onTabChange">
        <text>资料审核</text>
      </view>
      <view class="tab-item {{currentTab === 'users' ? 'active' : ''}}" 
            data-tab="users" bindtap="onTabChange">
        <text>用户管理</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <!-- 统计页面 -->
    <view wx:elif="{{currentTab === 'stats'}}" class="stats-content">
      <view class="stats-grid">
        <view class="stat-card">
          <view class="stat-number">{{stats.totalUsers}}</view>
          <view class="stat-label">总用户数</view>
        </view>
        <view class="stat-card">
          <view class="stat-number">{{stats.totalMaterials}}</view>
          <view class="stat-label">总资料数</view>
        </view>
        <view class="stat-card">
          <view class="stat-number">{{stats.totalDownloads}}</view>
          <view class="stat-label">总下载数</view>
        </view>
        <view class="stat-card">
          <view class="stat-number">{{stats.totalPoints}}</view>
          <view class="stat-label">总积分数</view>
        </view>
        <view class="stat-card">
          <view class="stat-number">{{stats.pendingMaterials}}</view>
          <view class="stat-label">待审核</view>
        </view>
        <view class="stat-card">
          <view class="stat-number">{{stats.activeUsers}}</view>
          <view class="stat-label">活跃用户</view>
        </view>
      </view>

      <view class="action-buttons">
        <button class="action-btn" bindtap="onRefresh" disabled="{{refreshing}}">
          {{refreshing ? '刷新中...' : '刷新数据'}}
        </button>
        <button class="action-btn secondary" bindtap="onExportData">
          导出数据
        </button>
      </view>
    </view>

    <!-- 资料审核页面 -->
    <view wx:elif="{{currentTab === 'materials'}}" class="materials-content">
      <view wx:if="{{pendingMaterials.length === 0}}" class="empty-state">
        <image src="/images/empty-materials.svg" class="empty-icon" mode="aspectFit"></image>
        <text class="empty-text">暂无待审核资料</text>
      </view>

      <view wx:else class="materials-list">
        <view wx:for="{{pendingMaterials}}" wx:key="_id" class="material-item">
          <view class="material-info">
            <view class="material-title">{{item.title}}</view>
            <view class="material-desc">{{item.description}}</view>
            <view class="material-meta">
              <text class="meta-item">{{item.grade}} | {{item.subject}}</text>
              <text class="meta-item">{{item.points}}积分</text>
              <text class="meta-item">{{formatTime(item.upload_time)}}</text>
            </view>
          </view>
          <view class="material-actions">
            <button class="approve-btn" 
                    data-material="{{item}}" 
                    data-action="approve" 
                    bindtap="onApproveMaterial">
              通过
            </button>
            <button class="reject-btn" 
                    data-material="{{item}}" 
                    data-action="reject" 
                    bindtap="onApproveMaterial">
              拒绝
            </button>
            <button class="view-btn" 
                    data-material="{{item}}" 
                    bindtap="onViewMaterial">
              查看
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 用户管理页面 -->
    <view wx:elif="{{currentTab === 'users'}}" class="users-content">
      <view wx:if="{{users.length === 0}}" class="empty-state">
        <image src="/images/empty-materials.svg" class="empty-icon" mode="aspectFit"></image>
        <text class="empty-text">暂无用户数据</text>
      </view>

      <view wx:else class="users-list">
        <view wx:for="{{users}}" wx:key="_id" class="user-item">
          <view class="user-info">
            <image src="{{item.avatarUrl || '/images/default-avatar.svg'}}" 
                   class="user-avatar" mode="aspectFill"></image>
            <view class="user-details">
              <view class="user-name">{{item.nickName || '未设置昵称'}}</view>
              <view class="user-meta">
                <text class="meta-item">积分: {{item.points || 0}}</text>
                <text class="meta-item">注册: {{formatTime(item.createTime)}}</text>
              </view>
            </view>
          </view>
          <view class="user-actions">
            <button wx:if="{{!item.isBanned}}" 
                    class="ban-btn" 
                    data-user="{{item}}" 
                    data-action="ban" 
                    bindtap="onManageUser">
              封禁
            </button>
            <button wx:else 
                    class="unban-btn" 
                    data-user="{{item}}" 
                    data-action="unban" 
                    bindtap="onManageUser">
              解封
            </button>
            <button class="points-btn" 
                    data-user="{{item}}" 
                    data-action="addPoints" 
                    bindtap="onManageUser">
              加积分
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
