// pages/feedback/feedback.js
const { showError, showSuccess, showLoading, hideLoading } = require('../../utils/util.js')

Page({
  data: {
    // 反馈类型
    feedbackTypes: [
      { id: 'bug', name: '问题反馈', icon: '/images/icon-bug.svg' },
      { id: 'feature', name: '功能建议', icon: '/images/icon-idea.svg' },
      { id: 'content', name: '内容问题', icon: '/images/icon-content.svg' },
      { id: 'other', name: '其他', icon: '/images/icon-other.svg' }
    ],
    
    // 表单数据
    formData: {
      type: '',
      title: '',
      description: '',
      contact: '',
      images: []
    },
    
    // 状态
    submitting: false,
    
    // 用户信息
    userInfo: null
  },

  onLoad(options) {
    this.initUserInfo()
  },

  // 初始化用户信息
  initUserInfo() {
    const app = getApp()
    this.setData({
      userInfo: app.getUserInfo()
    })
  },

  // 选择反馈类型
  onSelectType(e) {
    const { type } = e.currentTarget.dataset
    this.setData({
      'formData.type': type
    })
  },

  // 输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value
    })
  },

  // 选择图片
  async onChooseImages() {
    try {
      const result = await wx.chooseImage({
        count: 3 - this.data.formData.images.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      })

      if (result.tempFilePaths && result.tempFilePaths.length > 0) {
        const images = [...this.data.formData.images, ...result.tempFilePaths]
        this.setData({
          'formData.images': images
        })
      }
    } catch (error) {
      console.error('选择图片失败:', error)
      if (error.errMsg && !error.errMsg.includes('cancel')) {
        showError('选择图片失败')
      }
    }
  },

  // 预览图片
  onPreviewImage(e) {
    const { index } = e.currentTarget.dataset
    const { images } = this.data.formData
    
    wx.previewImage({
      current: images[index],
      urls: images
    })
  },

  // 删除图片
  onDeleteImage(e) {
    const { index } = e.currentTarget.dataset
    const images = this.data.formData.images
    images.splice(index, 1)
    
    this.setData({
      'formData.images': images
    })
  },

  // 提交反馈
  async onSubmit() {
    try {
      // 验证表单
      const validation = this.validateForm()
      if (!validation.valid) {
        showError(validation.message)
        return
      }

      this.setData({ submitting: true })
      showLoading('提交中...')

      // 上传图片
      const imageUrls = await this.uploadImages()

      // 提交反馈
      const result = await wx.cloud.callFunction({
        name: 'submitFeedback',
        data: {
          ...this.data.formData,
          images: imageUrls,
          userInfo: this.data.userInfo,
          deviceInfo: this.getDeviceInfo(),
          timestamp: new Date()
        }
      })

      if (result.result.success) {
        showSuccess('反馈提交成功，感谢您的建议！')
        // 重置表单
        this.resetForm()
      } else {
        showError(result.result.message || '提交失败')
      }

    } catch (error) {
      console.error('提交反馈失败:', error)
      showError('提交失败，请稍后重试')
    } finally {
      hideLoading()
      this.setData({ submitting: false })
    }
  },

  // 上传图片
  async uploadImages() {
    const { images } = this.data.formData
    if (images.length === 0) return []

    const uploadPromises = images.map(async (imagePath, index) => {
      try {
        const result = await wx.cloud.uploadFile({
          cloudPath: `feedback/${Date.now()}_${index}.jpg`,
          filePath: imagePath
        })
        return result.fileID
      } catch (error) {
        console.error('上传图片失败:', error)
        return null
      }
    })

    const results = await Promise.all(uploadPromises)
    return results.filter(fileId => fileId !== null)
  },

  // 验证表单
  validateForm() {
    const { formData } = this.data

    if (!formData.type) {
      return { valid: false, message: '请选择反馈类型' }
    }

    if (!formData.title.trim()) {
      return { valid: false, message: '请输入反馈标题' }
    }

    if (formData.title.length > 50) {
      return { valid: false, message: '标题长度不能超过50个字符' }
    }

    if (!formData.description.trim()) {
      return { valid: false, message: '请详细描述问题或建议' }
    }

    if (formData.description.length > 500) {
      return { valid: false, message: '描述长度不能超过500个字符' }
    }

    if (formData.contact && formData.contact.length > 50) {
      return { valid: false, message: '联系方式长度不能超过50个字符' }
    }

    return { valid: true }
  },

  // 重置表单
  resetForm() {
    this.setData({
      formData: {
        type: '',
        title: '',
        description: '',
        contact: '',
        images: []
      }
    })
  },

  // 获取设备信息
  getDeviceInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      return {
        brand: systemInfo.brand,
        model: systemInfo.model,
        system: systemInfo.system,
        platform: systemInfo.platform,
        version: systemInfo.version,
        SDKVersion: systemInfo.SDKVersion,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight
      }
    } catch (error) {
      console.error('获取设备信息失败:', error)
      return {}
    }
  },

  // 获取反馈类型名称
  getTypeName(typeId) {
    const type = this.data.feedbackTypes.find(t => t.id === typeId)
    return type ? type.name : '未知类型'
  }
})
