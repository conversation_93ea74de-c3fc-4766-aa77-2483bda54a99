<!--pages/simple-deploy/simple-deploy.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">🛠️ 简化部署</text>
    <text class="page-desc">无需云函数的基础配置方案</text>
  </view>

  <!-- 环境信息 -->
  <view class="env-info">
    <view class="env-title">🌍 环境信息</view>
    <view class="env-item">
      <text class="env-label">云环境ID:</text>
      <text class="env-value">{{envConfig.cloudEnvId}}</text>
    </view>
    <view class="env-item">
      <text class="env-label">当前环境:</text>
      <text class="env-value">{{envConfig.currentEnv}}</text>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <button class="action-btn primary" bindtap="onStartSimpleDeploy" disabled="{{deploying}}">
      {{deploying ? '配置中...' : '开始基础配置'}}
    </button>
    
    <view class="action-row">
      <button class="action-btn secondary" bindtap="onTestDatabase">
        测试数据库
      </button>
      <button class="action-btn secondary" bindtap="onInitData">
        初始化数据
      </button>
    </view>
  </view>

  <!-- 配置完成提示 -->
  <view wx:if="{{deployComplete}}" class="deploy-success">
    <text>🎉 基础配置完成！</text>
    <text>数据库和基础数据已准备就绪</text>
  </view>

  <!-- 配置步骤 -->
  <view class="deploy-steps">
    <view class="steps-title">📋 配置步骤</view>
    <view class="steps-list">
      <view wx:for="{{deploySteps}}" wx:key="id" 
            class="step-item {{item.status}} {{index <= currentStep ? 'active' : ''}}">
        <view class="step-header">
          <text class="step-icon">{{item.status === 'success' ? '✅' : item.status === 'failed' ? '❌' : item.status === 'running' ? '🔄' : '⏳'}}</text>
          <view class="step-content">
            <text class="step-name">{{item.name}}</text>
            <text class="step-desc">{{item.description}}</text>
          </view>
        </view>
        
        <view wx:if="{{item.error}}" class="step-error">
          <text>{{item.error}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 手动操作指南 -->
  <view class="manual-guide">
    <view class="guide-title">📖 手动操作指南</view>
    <view class="guide-content">
      <view wx:for="{{manualSteps}}" wx:key="title" class="guide-step">
        <view class="guide-step-title">{{item.title}}</view>
        <view class="guide-step-content">{{item.content}}</view>
      </view>
    </view>
    
    <button class="guide-btn" bindtap="onOpenConsoleGuide">
      如何打开云开发控制台？
    </button>
  </view>

  <!-- 功能说明 */
  <view class="feature-info">
    <view class="info-title">ℹ️ 功能说明</view>
    <view class="info-content">
      <text>• 此方案不依赖云函数部署</text>
      <text>• 直接通过小程序操作数据库</text>
      <text>• 适合快速启动和测试</text>
      <text>• 完成后可正常使用基础功能</text>
    </view>
  </view>

  <!-- 下一步提示 -->
  <view class="next-steps">
    <view class="next-title">🚀 下一步</view>
    <view class="next-content">
      <text>1. 完成基础配置后</text>
      <text>2. 可以开始使用小程序基础功能</text>
      <text>3. 后续可以逐步部署云函数增强功能</text>
      <text>4. 或使用完整版自动部署</text>
    </view>
  </view>
</view>
