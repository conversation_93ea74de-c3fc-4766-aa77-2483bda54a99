/**
 * 自动化云函数批量部署脚本
 * 基于 2025年最新 cloudbase CLI 工具
 */

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// 配置信息
const CONFIG = {
  envId: 'cloud1-8gm001v7fd56ff43',
  appId: 'wxdcb01784f343322b',
  projectPath: process.cwd(),
  cloudfunctionsPath: path.join(process.cwd(), 'cloudfunctions')
};

// 云函数列表（按优先级排序）
const CLOUD_FUNCTIONS = [
  // 第一批：核心功能（必须先部署）
  { name: 'initDatabase', priority: 1, description: '数据库初始化' },
  { name: 'getCategories', priority: 1, description: '获取分类数据' },
  { name: 'getRecommendMaterials', priority: 1, description: '获取推荐资料' },
  { name: 'login', priority: 1, description: '用户登录' },
  { name: 'searchMaterials', priority: 1, description: '搜索资料' },
  
  // 第二批：基础功能
  { name: 'getMaterialDetail', priority: 2, description: '获取资料详情' },
  { name: 'downloadMaterial', priority: 2, description: '下载资料' },
  { name: 'manageFavorite', priority: 2, description: '管理收藏' },
  { name: 'getMyDownloads', priority: 2, description: '获取我的下载' },
  { name: 'getMyFavorites', priority: 2, description: '获取我的收藏' },
  
  // 第三批：高级功能
  { name: 'generateShareCode', priority: 3, description: '生成分享码' },
  { name: 'handleShareInvite', priority: 3, description: '处理分享邀请' },
  { name: 'getShareStats', priority: 3, description: '获取分享统计' },
  { name: 'recordShareClick', priority: 3, description: '记录分享点击' },
  { name: 'uploadMaterial', priority: 3, description: '文件上传' },
  { name: 'validateFile', priority: 3, description: '文件验证' },
  { name: 'submitFeedback', priority: 3, description: '提交反馈' },
  { name: 'getAnnouncements', priority: 3, description: '获取公告' },
  { name: 'checkAdminPermission', priority: 3, description: '管理员权限检查' },
  { name: 'getAdminStats', priority: 3, description: '获取管理统计' }
];

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查环境和依赖
async function checkEnvironment() {
  colorLog('\n🔍 检查部署环境...', 'cyan');
  
  try {
    // 检查 cloudbase CLI 是否安装
    await execCommand('cloudbase --version');
    colorLog('✅ cloudbase CLI 已安装', 'green');
    
    // 检查登录状态
    try {
      await execCommand('cloudbase auth list');
      colorLog('✅ 已登录 cloudbase 账号', 'green');
    } catch (error) {
      colorLog('❌ 未登录 cloudbase 账号，请先执行: cloudbase login', 'red');
      process.exit(1);
    }
    
    // 检查云函数目录
    if (!fs.existsSync(CONFIG.cloudfunctionsPath)) {
      colorLog('❌ 未找到 cloudfunctions 目录', 'red');
      process.exit(1);
    }
    colorLog('✅ cloudfunctions 目录存在', 'green');
    
    // 检查云函数文件
    const missingFunctions = [];
    CLOUD_FUNCTIONS.forEach(func => {
      const funcPath = path.join(CONFIG.cloudfunctionsPath, func.name);
      if (!fs.existsSync(funcPath)) {
        missingFunctions.push(func.name);
      }
    });
    
    if (missingFunctions.length > 0) {
      colorLog(`❌ 缺少以下云函数: ${missingFunctions.join(', ')}`, 'red');
      process.exit(1);
    }
    colorLog(`✅ 所有 ${CLOUD_FUNCTIONS.length} 个云函数文件存在`, 'green');
    
  } catch (error) {
    colorLog('❌ cloudbase CLI 未安装，请先执行: npm install -g @cloudbase/cli', 'red');
    process.exit(1);
  }
}

// 执行命令的 Promise 封装
function execCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve(stdout);
      }
    });
  });
}

// 部署单个云函数
async function deployFunction(functionInfo) {
  const { name, description } = functionInfo;
  
  try {
    colorLog(`🚀 正在部署: ${name} (${description})`, 'yellow');
    
    const command = `cloudbase functions deploy ${name} -e ${CONFIG.envId}`;
    const result = await execCommand(command);
    
    colorLog(`✅ ${name} 部署成功`, 'green');
    return { success: true, name, result };
    
  } catch (error) {
    colorLog(`❌ ${name} 部署失败: ${error.message}`, 'red');
    return { success: false, name, error: error.message };
  }
}

// 验证云函数部署状态
async function verifyDeployment(functionName) {
  try {
    const command = `cloudbase functions list -e ${CONFIG.envId}`;
    const result = await execCommand(command);
    
    // 简单检查函数是否在列表中
    return result.includes(functionName);
  } catch (error) {
    return false;
  }
}

// 批量部署云函数
async function deployAllFunctions(options = {}) {
  const { priority = null, dryRun = false } = options;
  
  // 过滤要部署的函数
  let functionsToDeploy = CLOUD_FUNCTIONS;
  if (priority) {
    functionsToDeploy = CLOUD_FUNCTIONS.filter(func => func.priority === priority);
  }
  
  colorLog(`\n📋 准备部署 ${functionsToDeploy.length} 个云函数...`, 'cyan');
  colorLog(`🌍 目标环境: ${CONFIG.envId}`, 'cyan');
  
  if (dryRun) {
    colorLog('🔍 预演模式 - 不会实际部署', 'magenta');
    functionsToDeploy.forEach(func => {
      colorLog(`  - ${func.name} (${func.description})`, 'yellow');
    });
    return;
  }
  
  const results = {
    total: functionsToDeploy.length,
    success: 0,
    failed: 0,
    details: []
  };
  
  // 逐个部署云函数
  for (const functionInfo of functionsToDeploy) {
    const result = await deployFunction(functionInfo);
    results.details.push(result);
    
    if (result.success) {
      results.success++;
    } else {
      results.failed++;
    }
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 显示部署结果
  displayResults(results);
  
  return results;
}

// 显示部署结果
function displayResults(results) {
  colorLog('\n📊 部署结果统计:', 'cyan');
  colorLog(`📦 总计: ${results.total} 个`, 'blue');
  colorLog(`✅ 成功: ${results.success} 个`, 'green');
  colorLog(`❌ 失败: ${results.failed} 个`, 'red');
  
  if (results.failed > 0) {
    colorLog('\n❌ 失败的云函数:', 'red');
    results.details
      .filter(r => !r.success)
      .forEach(r => colorLog(`  - ${r.name}: ${r.error}`, 'red'));
  }
  
  if (results.success > 0) {
    colorLog('\n✅ 成功的云函数:', 'green');
    results.details
      .filter(r => r.success)
      .forEach(r => colorLog(`  - ${r.name}`, 'green'));
  }
  
  colorLog('\n🎉 批量部署完成!', 'bright');
}

// 主函数
async function main() {
  try {
    colorLog('🚀 微信小程序云函数自动化部署工具', 'bright');
    colorLog('📅 基于 2025年最新 cloudbase CLI', 'cyan');
    
    // 检查环境
    await checkEnvironment();
    
    // 获取命令行参数
    const args = process.argv.slice(2);
    const priority = args.includes('--priority') ? 
      parseInt(args[args.indexOf('--priority') + 1]) : null;
    const dryRun = args.includes('--dry-run');
    
    if (priority) {
      colorLog(`\n🎯 仅部署优先级 ${priority} 的云函数`, 'magenta');
    }
    
    // 开始部署
    const results = await deployAllFunctions({ priority, dryRun });
    
    if (!dryRun && results.success > 0) {
      colorLog('\n🔍 建议接下来:', 'cyan');
      colorLog('1. 运行小程序验证云函数状态', 'yellow');
      colorLog('2. 执行数据库初始化: 调用 initDatabase 云函数', 'yellow');
      colorLog('3. 测试核心功能是否正常工作', 'yellow');
    }
    
  } catch (error) {
    colorLog(`\n💥 部署过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  deployAllFunctions,
  deployFunction,
  checkEnvironment,
  CONFIG,
  CLOUD_FUNCTIONS
};
